{"FileVersion": 3, "Version": 1, "VersionName": "0.2", "FriendlyName": "Concert Sync Core", "Description": "Shared plugin for Concert Sync client and server plugins", "Category": "Networking", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": false, "Hidden": true, "IsBetaVersion": true, "Installed": false, "SupportedPrograms": ["UnrealMultiUserServer", "CoopMultiUserServer", "UnrealMultiUserSlateServer", "UnrealRecoverySvc", "CrashReportClientEditor", "LiveLinkHub"], "Modules": [{"Name": "ConcertSyncCore", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON>efault", "ProgramAllowList": ["UnrealMultiUserServer", "CoopMultiUserServer", "UnrealMultiUserSlateServer", "UnrealRecoverySvc", "CrashReportClientEditor", "LiveLinkHub"]}], "Plugins": [{"Name": "ConcertMain", "Enabled": true}, {"Name": "SQLiteCore", "Enabled": true}]}