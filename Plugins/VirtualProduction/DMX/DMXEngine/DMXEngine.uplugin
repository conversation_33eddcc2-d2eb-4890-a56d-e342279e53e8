{"FileVersion": 1, "Version": 1, "VersionName": "1.0", "FriendlyName": "DMX Engine", "Description": "Functionality and assets for communication with DigitalMultiplexer (DMX) enabled devices", "Category": "Virtual Production", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": false, "IsBetaVersion": false, "Modules": [{"Name": "DMXBlueprintGraph", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "DMXEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "DMXRuntime", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "DatasmithContent", "Enabled": true}, {"Name": "DMXGDTF", "Enabled": true}, {"Name": "DMXModularFeatures", "Enabled": true}, {"Name": "DMXProtocol", "Enabled": true}, {"Name": "Takes", "Enabled": true}]}