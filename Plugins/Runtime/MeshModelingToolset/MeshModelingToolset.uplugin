{"FileVersion": 3, "Version": 1, "VersionName": "0.1", "FriendlyName": "Mesh Modeling Toolset", "Description": "A set of modules implementing 3D mesh creation and editing based on the Interactive Tools Framework", "Category": "Other", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": true, "Installed": false, "Hidden": true, "Modules": [{"Name": "MeshModelingTools", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "MeshModelingToolsEditorOnly", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ModelingComponents", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ModelingComponentsEditorOnly", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ModelingOperators", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ModelingOperatorsEditorOnly", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "SkeletalMeshModifiers", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "GeometryProcessing", "Enabled": true}, {"Name": "ProxyLODPlugin", "Enabled": true}, {"Name": "PlanarCut", "Enabled": true}, {"Name": "GeometryCache", "Enabled": true}]}