{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Mobile Location Services - IOS Implementation", "Description": "IOS implementation for blueprint access for location data from mobile devices", "Category": "Mobile", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "EnabledByDefault": false, "CanContainContent": false, "IsBetaVersion": false, "Installed": false, "Modules": [{"Name": "LocationServicesIOSEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "LocationServicesIOSImpl", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["IOS"]}], "Plugins": [{"Name": "LocationServicesBPLibrary", "Enabled": true}], "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "IsExperimentalVersion": false}