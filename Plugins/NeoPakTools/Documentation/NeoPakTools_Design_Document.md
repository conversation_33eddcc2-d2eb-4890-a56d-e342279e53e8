# NeoPakTools 插件设计文档

## 1. 项目概述

### 1.1 插件目标
NeoPakTools是一个专为Unreal Engine设计的资产打包和运行时加载插件，支持将指定目录的资产打包为PAK文件，并提供运行时动态加载功能。

### 1.2 支持的资产类型
- **骨骼资产** (Skeleton Assets) - 基础骨骼结构
- **角色资产** (Character Assets) - 依赖骨骼资产
- **地图资产** (Map Assets) - 独立地图场景
- **动画资产** (Animation Assets) - 依赖骨骼资产
- **服装资产** (Clothing Assets) - 独立服装资源

### 1.3 依赖关系
```
骨骼资产 (基础)
├── 角色资产 (依赖骨骼)
└── 动画资产 (依赖骨骼)

地图资产 (独立)
服装资产 (独立)
```

## 2. 系统架构设计

### 2.1 模块结构
```
NeoPakTools/
├── Source/
│   ├── NeoPakTools/           # Runtime模块
│   │   ├── Public/
│   │   │   ├── NeoPakTools.h
│   │   │   ├── AssetTypes/
│   │   │   │   ├── NeoAssetType.h
│   │   │   │   ├── NeoSkeletonAsset.h
│   │   │   │   ├── NeoCharacterAsset.h
│   │   │   │   ├── NeoMapAsset.h
│   │   │   │   ├── NeoAnimationAsset.h
│   │   │   │   └── NeoClothingAsset.h
│   │   │   ├── PakManager/
│   │   │   │   ├── NeoPakManager.h
│   │   │   │   ├── NeoPakLoader.h
│   │   │   │   └── NeoPakValidator.h
│   │   │   └── Utils/
│   │   │       ├── NeoAssetDependencyResolver.h
│   │   │       └── NeoAssetRegistry.h
│   │   └── Private/
│   │       ├── NeoPakTools.cpp
│   │       ├── AssetTypes/
│   │       ├── PakManager/
│   │       └── Utils/
│   └── NeoPakToolsEditor/      # Editor模块
│       ├── Public/
│       │   ├── NeoPakToolsEditor.h
│       │   ├── UI/
│       │   │   ├── SNeoPakToolsWidget.h
│       │   │   └── NeoAssetPackagingWidget.h
│       │   └── Commands/
│       │       └── NeoPakToolsCommands.h
│       └── Private/
│           ├── NeoPakToolsEditor.cpp
│           ├── UI/
│           └── Commands/
└── Content/
    └── UI/
        └── Icons/
```

### 2.2 核心组件

#### 2.2.1 资产类型系统
```cpp
// 基础资产类型接口
class NEOPAKTOOLS_API INeoAssetType
{
public:
    virtual ~INeoAssetType() = default;
    virtual FString GetAssetTypeName() const = 0;
    virtual TArray<FString> GetDependencies() const = 0;
    virtual bool ValidateAsset(const FString& AssetPath) const = 0;
    virtual bool CanPackageIndependently() const = 0;
};

// 资产类型枚举
UENUM(BlueprintType)
enum class ENeoAssetType : uint8
{
    Skeleton    UMETA(DisplayName = "Skeleton Asset"),
    Character   UMETA(DisplayName = "Character Asset"),
    Map         UMETA(DisplayName = "Map Asset"),
    Animation   UMETA(DisplayName = "Animation Asset"),
    Clothing    UMETA(DisplayName = "Clothing Asset")
};
```

#### 2.2.2 PAK管理器
```cpp
class NEOPAKTOOLS_API UNeoPakManager : public UObject
{
    GENERATED_BODY()

public:
    // 打包功能
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool PackageDirectory(const FString& SourceDirectory, const FString& OutputPakPath, ENeoAssetType AssetType);
    
    // 运行时加载
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool LoadPakFile(const FString& PakFilePath);
    
    // 卸载PAK
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool UnloadPakFile(const FString& PakFilePath);
    
    // 依赖检查
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    TArray<FString> GetMissingDependencies(const FString& PakFilePath);

private:
    TMap<FString, TSharedPtr<class FNeoPakEntry>> LoadedPaks;
    TSharedPtr<class FNeoAssetDependencyResolver> DependencyResolver;
};
```

#### 2.2.3 依赖解析器
```cpp
class NEOPAKTOOLS_API FNeoAssetDependencyResolver
{
public:
    // 解析资产依赖
    TArray<FString> ResolveDependencies(const FString& AssetPath, ENeoAssetType AssetType);
    
    // 验证依赖完整性
    bool ValidateDependencies(const TArray<FString>& AssetPaths);
    
    // 获取打包顺序
    TArray<FString> GetPackagingOrder(const TArray<FString>& AssetPaths);

private:
    TMap<ENeoAssetType, TArray<ENeoAssetType>> DependencyMap;
};
```

## 3. 功能设计

### 3.1 编辑器工具

#### 3.1.1 主界面设计
- **资产浏览器**: 显示项目中可打包的资产
- **打包配置面板**: 设置打包参数和输出路径
- **依赖关系视图**: 可视化显示资产依赖关系
- **打包进度显示**: 实时显示打包进度和状态

#### 3.1.2 打包流程
1. **资产扫描**: 扫描指定目录下的资产
2. **类型识别**: 自动识别资产类型
3. **依赖分析**: 分析资产依赖关系
4. **依赖验证**: 确保所有依赖资产存在
5. **打包执行**: 按依赖顺序打包资产
6. **验证测试**: 验证生成的PAK文件完整性

#### 3.1.3 配置选项
```cpp
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoPakConfig
{
    GENERATED_BODY()

    // 源目录路径
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Packaging")
    FString SourceDirectory;
    
    // 输出PAK文件路径
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Packaging")
    FString OutputPakPath;
    
    // 资产类型
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Packaging")
    ENeoAssetType AssetType;
    
    // 是否包含依赖
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Packaging")
    bool bIncludeDependencies = true;
    
    // 压缩级别
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Packaging")
    int32 CompressionLevel = 6;
    
    // 是否加密
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Packaging")
    bool bEncryptPak = false;
};
```

### 3.2 运行时系统

#### 3.2.1 PAK加载器
```cpp
class NEOPAKTOOLS_API FNeoPakLoader
{
public:
    // 异步加载PAK文件
    TSharedPtr<FAsyncTask<class FLoadPakTask>> LoadPakAsync(const FString& PakFilePath);
    
    // 同步加载PAK文件
    bool LoadPakSync(const FString& PakFilePath);
    
    // 预加载依赖
    bool PreloadDependencies(const FString& PakFilePath);
    
    // 获取加载状态
    EPakLoadStatus GetLoadStatus(const FString& PakFilePath);

private:
    TMap<FString, EPakLoadStatus> PakLoadStates;
    FCriticalSection LoadStateLock;
};
```

#### 3.2.2 资产注册表
```cpp
class NEOPAKTOOLS_API FNeoAssetRegistry
{
public:
    // 注册已加载的资产
    void RegisterLoadedAsset(const FString& AssetPath, ENeoAssetType AssetType);
    
    // 查找资产
    TArray<FString> FindAssetsByType(ENeoAssetType AssetType);
    
    // 检查资产是否已加载
    bool IsAssetLoaded(const FString& AssetPath);
    
    // 获取资产信息
    FNeoAssetInfo GetAssetInfo(const FString& AssetPath);

private:
    TMap<FString, FNeoAssetInfo> RegisteredAssets;
    TMultiMap<ENeoAssetType, FString> AssetsByType;
};
```

### 3.3 依赖管理

#### 3.3.1 依赖规则
- **骨骼资产**: 无依赖，可独立打包
- **角色资产**: 必须依赖骨骼资产
- **动画资产**: 必须依赖骨骼资产
- **地图资产**: 可能依赖角色、服装等资产
- **服装资产**: 无强制依赖，可独立打包

#### 3.3.2 依赖检查算法
```cpp
bool FNeoAssetDependencyResolver::ValidateDependencies(const TArray<FString>& AssetPaths)
{
    for (const FString& AssetPath : AssetPaths)
    {
        ENeoAssetType AssetType = GetAssetType(AssetPath);
        TArray<FString> Dependencies = GetRequiredDependencies(AssetType);
        
        for (const FString& Dependency : Dependencies)
        {
            if (!IsAssetAvailable(Dependency))
            {
                UE_LOG(LogNeoPakTools, Error, TEXT("Missing dependency: %s for asset: %s"), 
                       *Dependency, *AssetPath);
                return false;
            }
        }
    }
    return true;
}
```

## 4. 用户界面设计

### 4.1 主工具窗口
- **菜单栏**: 文件操作、设置、帮助
- **工具栏**: 常用操作快捷按钮
- **资产列表**: 显示可打包的资产
- **属性面板**: 显示选中资产的详细信息
- **输出日志**: 显示操作日志和错误信息

### 4.2 打包向导
1. **选择资产类型**
2. **选择源目录**
3. **配置打包选项**
4. **依赖检查**
5. **执行打包**
6. **完成确认**

### 4.3 运行时监控面板
- **已加载PAK列表**
- **内存使用情况**
- **加载性能统计**
- **错误和警告信息**

## 5. 性能优化

### 5.1 打包优化
- **并行打包**: 支持多线程并行处理
- **增量打包**: 只打包修改过的资产
- **压缩优化**: 根据资产类型选择最佳压缩算法
- **依赖缓存**: 缓存依赖关系以提高后续打包速度

### 5.2 加载优化
- **异步加载**: 避免阻塞主线程
- **预加载策略**: 智能预测和预加载可能需要的资产
- **内存管理**: 及时释放不需要的资产
- **流式加载**: 支持大型资产的流式加载
