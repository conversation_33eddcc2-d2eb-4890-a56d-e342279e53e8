{"FileVersion": 3, "Version": 1, "VersionName": "1.0.0", "FriendlyName": "MetaHuman Creator", "Description": "MetaHuman Character Asset Creator and Editor.", "Category": "<PERSON><PERSON><PERSON><PERSON>", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "http://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": true, "IsExperimentalVersion": false, "Installed": false, "EnabledByDefault": false, "Modules": [{"Name": "MetaHumanCharacterEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanCharacter", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "MetaHumanCharacterPaletteEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "MetaHumanCharacterPalette", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "MetaHumanDefaultPipeline", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "MetaHumanDefaultEditorPipeline", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanCharacterMigrationEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "InterchangeDNA", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}], "Plugins": [{"Name": "MeshModelingToolset", "Enabled": true}, {"Name": "EditorScriptingUtilities", "Enabled": true}, {"Name": "AppleARKit", "Enabled": true, "TargetAllowList": ["Editor"]}, {"Name": "AppleARKitFaceSupport", "Enabled": true, "TargetAllowList": ["Editor"]}, {"Name": "LiveLink", "Enabled": true}, {"Name": "LiveLinkControlRig", "Enabled": true}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Enabled": true}, {"Name": "MetaHumanSDK", "Enabled": true}, {"Name": "HairStrands", "Enabled": true}, {"Name": "Dataflow", "Enabled": true}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Enabled": true, "TargetAllowList": ["Editor"]}, {"Name": "TextureGraph", "Enabled": true, "TargetAllowList": ["Editor"]}, {"Name": "ChaosClothAsset", "Enabled": true}, {"Name": "ChaosClothAssetEditor", "Enabled": true}, {"Name": "ChaosOutfitAsset", "Enabled": true}, {"Name": "PerformanceCaptureCore", "Enabled": true}, {"Name": "IKRig", "Enabled": true}, {"Name": "Plug<PERSON><PERSON><PERSON><PERSON>", "Enabled": true}, {"Name": "GeometryScripting", "Enabled": true, "TargetAllowList": ["Editor"]}], "LocalizationTargets": [{"Name": "MetaHumanCharacter", "LoadingPolicy": "Editor", "ConfigGenerationPolicy": "Auto"}]}