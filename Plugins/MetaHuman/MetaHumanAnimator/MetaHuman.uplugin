{"FileVersion": 3, "Version": 1, "VersionName": "5.0.0", "FriendlyName": "<PERSON><PERSON><PERSON><PERSON> Animator", "Description": "The official MetaHuman Unreal Engine toolkit", "Category": "<PERSON><PERSON><PERSON><PERSON>", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "http://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": false, "Installed": false, "SupportedTargetPlatforms": ["Win64", "Linux"], "Modules": [{"Name": "MetaHumanCore", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanCoreEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanImageViewerEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "Meta<PERSON>uman<PERSON><PERSON><PERSON><PERSON>", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanFaceContourTracker", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanFaceContourTrackerEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanIdentity", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanIdentityEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanCaptureDataEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanCaptureSource", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanFootageIngest", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanDepthGenerator", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanPerformance", "Type": "Editor", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanSequencer", "Type": "Editor", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanCaptureUtils", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanCaptureProtocolStack", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanToolkit", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanSpeech2Face", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanBatchProcessor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanConfig", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanConfigEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanFaceAnimationSolver", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanFaceAnimationSolverEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanFaceFittingSolver", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanFaceFittingSolverEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanPlatform", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MetaHumanControlsConversionTest", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "DNAInterchange", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}, {"Name": "MeshTrackerInterface", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64"]}], "Plugins": [{"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Enabled": true}, {"Name": "NNERuntimeORT", "Enabled": true}, {"Name": "MeshModelingToolset", "Enabled": true}, {"Name": "ImgMedia", "Enabled": true}, {"Name": "CaptureData", "Enabled": true}, {"Name": "CaptureManagerEditor", "Enabled": true}, {"Name": "ProceduralMeshComponent", "Enabled": true}, {"Name": "MediaCompositing", "Enabled": true}, {"Name": "CameraCalibrationCore", "Enabled": true}, {"Name": "LensComponent", "Enabled": true}, {"Name": "MediaIOFramework", "Enabled": true}, {"Name": "PlatformCrypto", "Enabled": true}, {"Name": "Interchange", "Enabled": true}, {"Name": "LiveLinkHub", "Enabled": true}, {"Name": "DNACalib", "Enabled": true}, {"Name": "MetaHumanCoreTech", "Enabled": true}, {"Name": "MetaHumanSDK", "Enabled": true}], "LocalizationTargets": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "LoadingPolicy": "Editor", "ConfigGenerationPolicy": "Auto"}]}