{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "VP8 and VP9 software decoder for Electra", "Description": "Implements VP8 and VP9 playback with the Electra media player on desktop machines", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "Category": "Media Players", "EnabledByDefault": false, "CanContainContent": false, "IsBetaVersion": false, "Installed": false, "Modules": [{"Name": "VPxDecoderElectra", "Type": "Runtime", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64", "<PERSON>", "Linux"], "TargetDenyList": ["Server"]}], "Plugins": [{"Name": "ElectraCodecs", "Enabled": true}]}