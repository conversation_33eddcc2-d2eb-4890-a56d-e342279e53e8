{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Description": "A DaySequence implementation of a Celestial Vault for Earth using ephemeris", "Category": "Other", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "Modules": [{"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "CelestialVaultEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "DaySequence", "Enabled": true}]}