<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Opus 1.0.3</Name>
  <Location>/Engine/Source/ThirdParty/libOpus/opus-1.0.3/</Location>
  <Date>2016-06-13T16:32:17.8064692-04:00</Date>
  <Function>Low latency, cross platform audio codec for VOIP</Function>
  <Justification />
  <Eula>http://www.opus-codec.org/license/</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/Opus1.0.3_License.txt</LicenseFolder>
</TpsData>