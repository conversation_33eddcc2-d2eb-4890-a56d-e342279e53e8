# NeoPakTools 插件设计文档

## 1. 项目概述

### 1.1 插件目标
NeoPakTools是一个专为Unreal Engine设计的资产打包和运行时加载插件，支持将指定目录的资产打包为PAK文件，并提供运行时动态加载功能。

### 1.2 支持的资产类型
- **骨骼资产** (Skeleton Assets) - 基础骨骼结构
- **角色资产** (Character Assets) - 依赖骨骼资产
- **地图资产** (Map Assets) - 独立地图场景
- **动画资产** (Animation Assets) - 依赖骨骼资产
- **服装资产** (Clothing Assets) - 独立服装资源

### 1.3 依赖关系
```
骨骼资产 (基础)
├── 角色资产 (依赖骨骼)
└── 动画资产 (依赖骨骼)

地图资产 (独立)
服装资产 (独立)
```

## 2. 系统架构设计

### 2.1 模块结构
```
NeoPakTools/
├── Source/
│   ├── NeoPakTools/           # Runtime模块
│   │   ├── Public/
│   │   │   ├── NeoPakTools.h
│   │   │   ├── AssetTypes/
│   │   │   │   ├── NeoAssetType.h
│   │   │   │   ├── NeoSkeletonAsset.h
│   │   │   │   ├── NeoCharacterAsset.h
│   │   │   │   ├── NeoMapAsset.h
│   │   │   │   ├── NeoAnimationAsset.h
│   │   │   │   └── NeoClothingAsset.h
│   │   │   ├── PakManager/
│   │   │   │   ├── NeoPakManager.h
│   │   │   │   ├── NeoPakLoader.h
│   │   │   │   └── NeoPakValidator.h
│   │   │   └── Utils/
│   │   │       ├── NeoAssetDependencyResolver.h
│   │   │       └── NeoAssetRegistry.h
│   │   └── Private/
│   │       ├── NeoPakTools.cpp
│   │       ├── AssetTypes/
│   │       ├── PakManager/
│   │       └── Utils/
│   └── NeoPakToolsEditor/      # Editor模块
│       ├── Public/
│       │   ├── NeoPakToolsEditor.h
│       │   ├── UI/
│       │   │   ├── SNeoPakToolsWidget.h
│       │   │   └── NeoAssetPackagingWidget.h
│       │   └── Commands/
│       │       └── NeoPakToolsCommands.h
│       └── Private/
│           ├── NeoPakToolsEditor.cpp
│           ├── UI/
│           └── Commands/
└── Content/
    └── UI/
        └── Icons/
```

### 2.2 核心组件

#### 2.2.1 资产类型系统
```cpp
// 基础资产类型接口
class NEOPAKTOOLS_API INeoAssetType
{
public:
    virtual ~INeoAssetType() = default;
    virtual FString GetAssetTypeName() const = 0;
    virtual TArray<FString> GetDependencies() const = 0;
    virtual bool ValidateAsset(const FString& AssetPath) const = 0;
    virtual bool CanPackageIndependently() const = 0;
};

// 资产类型枚举
UENUM(BlueprintType)
enum class ENeoAssetType : uint8
{
    Skeleton    UMETA(DisplayName = "Skeleton Asset"),
    Character   UMETA(DisplayName = "Character Asset"),
    Map         UMETA(DisplayName = "Map Asset"),
    Animation   UMETA(DisplayName = "Animation Asset"),
    Clothing    UMETA(DisplayName = "Clothing Asset")
};
```

#### 2.2.2 PAK管理器
```cpp
class NEOPAKTOOLS_API UNeoPakManager : public UObject
{
    GENERATED_BODY()

public:
    // 打包功能
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool PackageDirectory(const FString& SourceDirectory, const FString& OutputPakPath, ENeoAssetType AssetType);
    
    // 运行时加载
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool LoadPakFile(const FString& PakFilePath);
    
    // 卸载PAK
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool UnloadPakFile(const FString& PakFilePath);
    
    // 依赖检查
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    TArray<FString> GetMissingDependencies(const FString& PakFilePath);

private:
    TMap<FString, TSharedPtr<class FNeoPakEntry>> LoadedPaks;
    TSharedPtr<class FNeoAssetDependencyResolver> DependencyResolver;
};
```

#### 2.2.3 依赖解析器
```cpp
class NEOPAKTOOLS_API FNeoAssetDependencyResolver
{
public:
    // 解析资产依赖
    TArray<FString> ResolveDependencies(const FString& AssetPath, ENeoAssetType AssetType);
    
    // 验证依赖完整性
    bool ValidateDependencies(const TArray<FString>& AssetPaths);
    
    // 获取打包顺序
    TArray<FString> GetPackagingOrder(const TArray<FString>& AssetPaths);

private:
    TMap<ENeoAssetType, TArray<ENeoAssetType>> DependencyMap;
};
```

## 3. 功能设计

### 3.1 编辑器工具

#### 3.1.1 主界面设计
- **资产浏览器**: 显示项目中可打包的资产
- **打包配置面板**: 设置打包参数和输出路径
- **依赖关系视图**: 可视化显示资产依赖关系
- **打包进度显示**: 实时显示打包进度和状态

#### 3.1.2 打包流程
1. **资产扫描**: 扫描指定目录下的资产
2. **类型识别**: 自动识别资产类型
3. **依赖分析**: 分析资产依赖关系
4. **依赖验证**: 确保所有依赖资产存在
5. **打包执行**: 按依赖顺序打包资产
6. **验证测试**: 验证生成的PAK文件完整性

#### 3.1.3 配置选项
```cpp
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoPakConfig
{
    GENERATED_BODY()

    // 源目录路径
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Packaging")
    FString SourceDirectory;
    
    // 输出PAK文件路径
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Packaging")
    FString OutputPakPath;
    
    // 资产类型
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Packaging")
    ENeoAssetType AssetType;
    
    // 是否包含依赖
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Packaging")
    bool bIncludeDependencies = true;
    
    // 压缩级别
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Packaging")
    int32 CompressionLevel = 6;
    
    // 是否加密
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Packaging")
    bool bEncryptPak = false;
};
```

### 3.2 运行时系统

#### 3.2.1 PAK加载器
```cpp
class NEOPAKTOOLS_API FNeoPakLoader
{
public:
    // 异步加载PAK文件
    TSharedPtr<FAsyncTask<class FLoadPakTask>> LoadPakAsync(const FString& PakFilePath);
    
    // 同步加载PAK文件
    bool LoadPakSync(const FString& PakFilePath);
    
    // 预加载依赖
    bool PreloadDependencies(const FString& PakFilePath);
    
    // 获取加载状态
    EPakLoadStatus GetLoadStatus(const FString& PakFilePath);

private:
    TMap<FString, EPakLoadStatus> PakLoadStates;
    FCriticalSection LoadStateLock;
};
```

#### 3.2.2 资产注册表
```cpp
class NEOPAKTOOLS_API FNeoAssetRegistry
{
public:
    // 注册已加载的资产
    void RegisterLoadedAsset(const FString& AssetPath, ENeoAssetType AssetType);
    
    // 查找资产
    TArray<FString> FindAssetsByType(ENeoAssetType AssetType);
    
    // 检查资产是否已加载
    bool IsAssetLoaded(const FString& AssetPath);
    
    // 获取资产信息
    FNeoAssetInfo GetAssetInfo(const FString& AssetPath);

private:
    TMap<FString, FNeoAssetInfo> RegisteredAssets;
    TMultiMap<ENeoAssetType, FString> AssetsByType;
};
```

### 3.3 依赖管理

#### 3.3.1 依赖规则
- **骨骼资产**: 无依赖，可独立打包
- **角色资产**: 必须依赖骨骼资产
- **动画资产**: 必须依赖骨骼资产
- **地图资产**: 可能依赖角色、服装等资产
- **服装资产**: 无强制依赖，可独立打包

#### 3.3.2 依赖检查算法
```cpp
bool FNeoAssetDependencyResolver::ValidateDependencies(const TArray<FString>& AssetPaths)
{
    for (const FString& AssetPath : AssetPaths)
    {
        ENeoAssetType AssetType = GetAssetType(AssetPath);
        TArray<FString> Dependencies = GetRequiredDependencies(AssetType);
        
        for (const FString& Dependency : Dependencies)
        {
            if (!IsAssetAvailable(Dependency))
            {
                UE_LOG(LogNeoPakTools, Error, TEXT("Missing dependency: %s for asset: %s"), 
                       *Dependency, *AssetPath);
                return false;
            }
        }
    }
    return true;
}
```

## 4. 用户界面设计

### 4.1 主工具窗口
- **菜单栏**: 文件操作、设置、帮助
- **工具栏**: 常用操作快捷按钮
- **资产列表**: 显示可打包的资产
- **属性面板**: 显示选中资产的详细信息
- **输出日志**: 显示操作日志和错误信息

### 4.2 打包向导
1. **选择资产类型**
2. **选择源目录**
3. **配置打包选项**
4. **依赖检查**
5. **执行打包**
6. **完成确认**

### 4.3 运行时监控面板
- **已加载PAK列表**
- **内存使用情况**
- **加载性能统计**
- **错误和警告信息**

## 5. 性能优化

### 5.1 打包优化
- **并行打包**: 支持多线程并行处理
- **增量打包**: 只打包修改过的资产
- **压缩优化**: 根据资产类型选择最佳压缩算法
- **依赖缓存**: 缓存依赖关系以提高后续打包速度

### 5.2 加载优化
- **异步加载**: 避免阻塞主线程
- **预加载策略**: 智能预测和预加载可能需要的资产
- **内存管理**: 及时释放不需要的资产
- **流式加载**: 支持大型资产的流式加载

## 6. 错误处理和日志

### 6.1 错误类型
- **依赖缺失错误**
- **文件访问错误**
- **内存不足错误**
- **格式不兼容错误**

### 6.2 日志系统
```cpp
DECLARE_LOG_CATEGORY_EXTERN(LogNeoPakTools, Log, All);

// 使用示例
UE_LOG(LogNeoPakTools, Warning, TEXT("Asset dependency missing: %s"), *AssetPath);
UE_LOG(LogNeoPakTools, Error, TEXT("Failed to load PAK file: %s"), *PakFilePath);
```

## 7. 配置和设置

### 7.1 项目设置
- **默认输出路径**
- **压缩设置**
- **加密选项**
- **依赖检查级别**

### 7.2 用户偏好设置
- **界面布局**
- **日志级别**
- **自动保存选项**
- **快捷键配置**

## 8. 扩展性设计

### 8.1 插件接口
```cpp
class NEOPAKTOOLS_API INeoAssetTypePlugin
{
public:
    virtual ~INeoAssetTypePlugin() = default;
    virtual void RegisterAssetType(TSharedPtr<INeoAssetType> AssetType) = 0;
    virtual void UnregisterAssetType(const FString& TypeName) = 0;
};
```

### 8.2 自定义资产类型
支持用户定义新的资产类型和依赖规则，通过插件系统进行扩展。

## 9. 测试策略

### 9.1 单元测试
- 资产类型识别测试
- 依赖解析测试
- PAK文件操作测试

### 9.2 集成测试
- 完整打包流程测试
- 运行时加载测试
- 性能压力测试

### 9.3 用户验收测试
- 界面易用性测试
- 功能完整性测试
- 错误处理测试

## 10. 部署和维护

### 10.1 版本管理
- 语义化版本控制
- 向后兼容性保证
- 升级路径规划

### 10.2 文档和支持
- 用户手册
- API文档
- 示例项目
- 常见问题解答

## 11. 实现计划

### 11.1 开发阶段

#### 阶段1: 核心框架 (4周)
- 基础模块结构搭建
- 资产类型系统实现
- 基础PAK管理器实现

#### 阶段2: 编辑器工具 (6周)
- 编辑器UI界面开发
- 打包流程实现
- 依赖解析器开发

#### 阶段3: 运行时系统 (4周)
- PAK加载器实现
- 资产注册表开发
- 异步加载系统

#### 阶段4: 优化和测试 (4周)
- 性能优化
- 错误处理完善
- 全面测试

#### 阶段5: 文档和发布 (2周)
- 用户文档编写
- 示例项目制作
- 最终发布准备

### 11.2 里程碑
- **M1**: 核心框架完成
- **M2**: 基础打包功能可用
- **M3**: 运行时加载功能完成
- **M4**: Beta版本发布
- **M5**: 正式版本发布

## 12. 风险评估

### 12.1 技术风险
- **UE版本兼容性**: 不同UE版本的PAK格式差异
- **性能瓶颈**: 大量资产加载时的性能问题
- **内存管理**: 运行时内存使用优化

### 12.2 缓解措施
- 多版本测试和兼容性适配
- 性能分析和优化工具使用
- 内存池和智能指针管理

## 13. 详细技术规范

### 13.1 PAK文件格式扩展
```cpp
struct FNeoPakHeader
{
    uint32 Magic;                    // 'NPAK'
    uint32 Version;                  // 版本号
    ENeoAssetType AssetType;         // 资产类型
    uint32 DependencyCount;          // 依赖数量
    uint64 DependencyOffset;         // 依赖信息偏移
    uint64 AssetDataOffset;          // 资产数据偏移
    uint32 Checksum;                 // 校验和
};

struct FNeoDependencyInfo
{
    FString DependencyPath;          // 依赖路径
    ENeoAssetType DependencyType;    // 依赖类型
    uint32 DependencyHash;           // 依赖哈希
};
```

### 13.2 资产加载状态机
```cpp
UENUM(BlueprintType)
enum class EPakLoadStatus : uint8
{
    NotLoaded       UMETA(DisplayName = "Not Loaded"),
    Loading         UMETA(DisplayName = "Loading"),
    Loaded          UMETA(DisplayName = "Loaded"),
    Failed          UMETA(DisplayName = "Failed"),
    Unloading       UMETA(DisplayName = "Unloading")
};
```

### 13.3 蓝图接口设计
```cpp
UCLASS(BlueprintType, Blueprintable)
class NEOPAKTOOLS_API UNeoPakToolsBlueprintLibrary : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    // 蓝图可调用的打包函数
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools", CallInEditor = true)
    static bool PackageAssets(const TArray<FString>& AssetPaths, const FString& OutputPath, ENeoAssetType AssetType);

    // 蓝图可调用的加载函数
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    static bool LoadPakFileBlueprint(const FString& PakFilePath);

    // 获取已加载的PAK列表
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    static TArray<FString> GetLoadedPakFiles();

    // 检查资产是否可用
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    static bool IsAssetAvailable(const FString& AssetPath);
};
```

## 14. 安全和加密

### 14.1 PAK文件加密
- **AES-256加密**: 支持PAK文件内容加密
- **密钥管理**: 安全的密钥存储和分发机制
- **签名验证**: 防止PAK文件被篡改

### 14.2 访问控制
```cpp
class NEOPAKTOOLS_API FNeoSecurityManager
{
public:
    // 验证PAK文件签名
    bool VerifyPakSignature(const FString& PakFilePath);

    // 解密PAK文件
    bool DecryptPakFile(const FString& PakFilePath, const FString& DecryptionKey);

    // 检查访问权限
    bool CheckAccessPermission(const FString& AssetPath, const FString& UserToken);
};
```

## 15. 国际化支持

### 15.1 多语言界面
- 支持中文、英文等多种语言
- 动态语言切换
- 本地化资源管理

### 15.2 本地化配置
```cpp
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoLocalizationConfig
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Localization")
    FString DefaultLanguage = TEXT("zh-CN");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Localization")
    TMap<FString, FString> LanguageDisplayNames;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Localization")
    bool bAutoDetectSystemLanguage = true;
};
```

## 16. 总结

NeoPakTools插件将为Unreal Engine开发者提供一个强大而灵活的资产打包和运行时加载解决方案。通过模块化设计、完善的依赖管理和优化的性能表现，该插件将显著提升开发效率和游戏运行时的资源管理能力。

### 16.1 核心优势
- **类型化资产管理**: 针对不同资产类型的专门处理
- **智能依赖解析**: 自动处理复杂的资产依赖关系
- **高性能加载**: 异步加载和内存优化
- **易用的界面**: 直观的编辑器工具和蓝图接口
- **扩展性强**: 支持自定义资产类型和插件扩展

### 16.2 应用场景
- **DLC内容分发**: 游戏追加内容的打包和分发
- **模块化开发**: 大型项目的模块化资源管理
- **热更新系统**: 运行时资源更新和替换
- **内容流式加载**: 开放世界游戏的动态内容加载

---

**文档版本**: 1.0
**创建日期**: 2025-08-01
**作者**: Neo
**审核状态**: 待审核
**文档类型**: 技术设计文档
