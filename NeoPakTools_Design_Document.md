# NeoPakTools 插件设计文档

## 📋 文档目录

### 核心架构
1. **项目概述** - 插件目标和资产类型
2. **系统架构设计** - 基于UE子系统的模块化架构
3. **子系统设计理念** - 为什么使用子系统及其优势

### 功能实现
4. **功能设计** - 编辑器工具和运行时系统
5. **用户界面设计** - Slate UI和蓝图集成
6. **性能优化** - 异步加载和内存管理

### 技术细节
7. **错误处理和日志** - 统一的错误处理机制
8. **配置和设置** - 项目设置和用户偏好
9. **扩展性设计** - 插件接口和自定义资产类型
10. **安全和加密** - 文件加密和访问控制

### 开发指南
11. **测试策略** - 单元测试和集成测试
12. **部署和维护** - 版本管理和文档支持
13. **实现计划** - 开发阶段和里程碑
14. **风险评估** - 技术风险和缓解措施

### 高级特性
15. **详细技术规范** - PAK文件格式和蓝图接口
16. **国际化支持** - 多语言界面和本地化
17. **子系统最佳实践和使用指南** - 性能优化和调试技巧

---

## 🎯 设计亮点

### 🏗️ 基于UE子系统的现代架构
- **GameInstanceSubsystem**: 运行时PAK管理、资产管理、依赖管理
- **EditorSubsystem**: 编辑器工具、打包功能、资产验证
- **消息总线**: 子系统间松耦合通信机制

### ⚡ 高性能设计
- **异步加载**: 避免阻塞主线程的PAK加载
- **智能缓存**: 依赖关系和资产信息缓存
- **内存管理**: 自动清理和弱引用管理

### 🔧 开发者友好
- **蓝图集成**: 完整的蓝图函数库支持
- **编辑器工具**: 直观的Slate UI界面
- **调试支持**: 详细的日志和性能分析工具

---

## 1. 项目概述

### 1.1 插件目标
NeoPakTools是一个专为Unreal Engine设计的现代化资产打包和运行时加载插件，采用UE子系统架构，支持将指定目录的资产打包为PAK文件，并提供高性能的运行时动态加载功能。

#### 核心特性
- **🏗️ 子系统架构**: 基于UE子系统的模块化设计，确保生命周期管理和全局访问
- **⚡ 高性能加载**: 异步PAK加载和智能资产管理，避免阻塞游戏主线程
- **🔗 智能依赖管理**: 自动解析和验证资产依赖关系，确保加载顺序正确
- **🎨 编辑器集成**: 完整的Slate UI编辑器工具，支持可视化打包和管理
- **🔧 蓝图友好**: 丰富的蓝图接口，支持设计师和程序员协作开发
- **📊 调试支持**: 详细的日志系统和性能分析工具，便于问题诊断

### 1.2 支持的资产类型
- **骨骼资产** (Skeleton Assets) - 基础骨骼结构
- **角色资产** (Character Assets) - 依赖骨骼资产
- **地图资产** (Map Assets) - 独立地图场景
- **动画资产** (Animation Assets) - 依赖骨骼资产
- **服装资产** (Clothing Assets) - 独立服装资源

### 1.3 依赖关系
```
骨骼资产 (基础)
├── 角色资产 (依赖骨骼)
└── 动画资产 (依赖骨骼)

地图资产 (独立)
服装资产 (独立)
```

## 2. 系统架构设计

### 2.1 模块结构
```
NeoPakTools/
├── Source/
│   ├── NeoPakTools/           # Runtime模块
│   │   ├── Public/
│   │   │   ├── NeoPakTools.h
│   │   │   ├── Subsystems/
│   │   │   │   ├── NeoPakSubsystem.h
│   │   │   │   ├── NeoAssetManagerSubsystem.h
│   │   │   │   └── NeoDependencySubsystem.h
│   │   │   ├── AssetTypes/
│   │   │   │   ├── NeoAssetType.h
│   │   │   │   ├── NeoSkeletonAsset.h
│   │   │   │   ├── NeoCharacterAsset.h
│   │   │   │   ├── NeoMapAsset.h
│   │   │   │   ├── NeoAnimationAsset.h
│   │   │   │   └── NeoClothingAsset.h
│   │   │   ├── PakManager/
│   │   │   │   ├── NeoPakManager.h
│   │   │   │   ├── NeoPakLoader.h
│   │   │   │   └── NeoPakValidator.h
│   │   │   └── Utils/
│   │   │       ├── NeoAssetDependencyResolver.h
│   │   │       └── NeoAssetRegistry.h
│   │   └── Private/
│   │       ├── NeoPakTools.cpp
│   │       ├── Subsystems/
│   │       │   ├── NeoPakSubsystem.cpp
│   │       │   ├── NeoAssetManagerSubsystem.cpp
│   │       │   └── NeoDependencySubsystem.cpp
│   │       ├── AssetTypes/
│   │       ├── PakManager/
│   │       └── Utils/
│   └── NeoPakToolsEditor/      # Editor模块
│       ├── Public/
│       │   ├── NeoPakToolsEditor.h
│       │   ├── Subsystems/
│       │   │   ├── NeoPakEditorSubsystem.h
│       │   │   └── NeoPackagingSubsystem.h
│       │   ├── UI/
│       │   │   ├── SNeoPakToolsWidget.h
│       │   │   └── NeoAssetPackagingWidget.h
│       │   └── Commands/
│       │       └── NeoPakToolsCommands.h
│       └── Private/
│           ├── NeoPakToolsEditor.cpp
│           ├── Subsystems/
│           │   ├── NeoPakEditorSubsystem.cpp
│           │   └── NeoPackagingSubsystem.cpp
│           ├── UI/
│           └── Commands/
└── Content/
    └── UI/
        └── Icons/
```

### 2.2 子系统架构

#### 2.2.1 运行时子系统

##### NeoPakSubsystem (GameInstanceSubsystem)
```cpp
UCLASS()
class NEOPAKTOOLS_API UNeoPakSubsystem : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    // PAK管理功能
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool LoadPakFile(const FString& PakFilePath);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool UnloadPakFile(const FString& PakFilePath);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    TArray<FString> GetLoadedPakFiles() const;

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    EPakLoadStatus GetPakLoadStatus(const FString& PakFilePath) const;

    // 事件委托
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPakLoaded, const FString&, PakFilePath, bool, bSuccess);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPakUnloaded, const FString&, PakFilePath, bool, bSuccess);

    UPROPERTY(BlueprintAssignable, Category = "NeoPakTools")
    FOnPakLoaded OnPakLoaded;

    UPROPERTY(BlueprintAssignable, Category = "NeoPakTools")
    FOnPakUnloaded OnPakUnloaded;

private:
    UPROPERTY()
    TObjectPtr<class UNeoPakManager> PakManager;

    TMap<FString, EPakLoadStatus> PakLoadStates;
};
```

##### NeoAssetManagerSubsystem (GameInstanceSubsystem)
```cpp
UCLASS()
class NEOPAKTOOLS_API UNeoAssetManagerSubsystem : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    // 资产管理功能
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool IsAssetLoaded(const FString& AssetPath) const;

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    TArray<FString> GetLoadedAssetsByType(ENeoAssetType AssetType) const;

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    UObject* LoadAssetFromPak(const FString& AssetPath);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    void UnloadAsset(const FString& AssetPath);

    // 异步加载
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    void LoadAssetAsync(const FString& AssetPath, FOnAssetLoaded OnLoaded);

    DECLARE_DYNAMIC_DELEGATE_TwoParams(FOnAssetLoaded, UObject*, LoadedAsset, bool, bSuccess);

private:
    UPROPERTY()
    TObjectPtr<class FNeoAssetRegistry> AssetRegistry;

    TMap<FString, TWeakObjectPtr<UObject>> LoadedAssets;
};
```

##### NeoDependencySubsystem (GameInstanceSubsystem)
```cpp
UCLASS()
class NEOPAKTOOLS_API UNeoDependencySubsystem : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;

    // 依赖管理功能
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    TArray<FString> GetAssetDependencies(const FString& AssetPath) const;

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool ValidateAssetDependencies(const FString& AssetPath) const;

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    TArray<FString> GetMissingDependencies(const FString& AssetPath) const;

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    bool PreloadDependencies(const FString& AssetPath);

private:
    UPROPERTY()
    TObjectPtr<class FNeoAssetDependencyResolver> DependencyResolver;
};
```

#### 2.2.2 编辑器子系统

##### NeoPakEditorSubsystem (EditorSubsystem)
```cpp
UCLASS()
class NEOPAKTOOLSEDITOR_API UNeoPakEditorSubsystem : public UEditorSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    // 编辑器功能
    UFUNCTION(CallInEditor = true, Category = "NeoPakTools")
    bool PackageAssets(const TArray<FString>& AssetPaths, const FString& OutputPath, ENeoAssetType AssetType);

    UFUNCTION(CallInEditor = true, Category = "NeoPakTools")
    bool ValidatePackagingConfiguration(const FNeoPakConfig& Config);

    UFUNCTION(CallInEditor = true, Category = "NeoPakTools")
    TArray<FString> ScanAssetsInDirectory(const FString& DirectoryPath);

    // 事件委托
    DECLARE_MULTICAST_DELEGATE_ThreeParams(FOnPackagingProgress, float, Progress, const FString&, CurrentAsset, const FString&, Status);
    DECLARE_MULTICAST_DELEGATE_TwoParams(FOnPackagingComplete, bool, bSuccess, const FString&, OutputPath);

    FOnPackagingProgress OnPackagingProgress;
    FOnPackagingComplete OnPackagingComplete;

private:
    UPROPERTY()
    TObjectPtr<class UNeoPackagingSubsystem> PackagingSubsystem;
};
```

##### NeoPackagingSubsystem (EditorSubsystem)
```cpp
UCLASS()
class NEOPAKTOOLSEDITOR_API UNeoPackagingSubsystem : public UEditorSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;

    // 打包功能
    bool ExecutePackaging(const FNeoPakConfig& Config);
    bool ValidateAssets(const TArray<FString>& AssetPaths);
    void CancelPackaging();

    // 进度监控
    float GetPackagingProgress() const { return PackagingProgress; }
    FString GetCurrentPackagingAsset() const { return CurrentPackagingAsset; }
    bool IsPackaging() const { return bIsPackaging; }

private:
    void OnPackagingStep(const FString& AssetPath);
    void OnPackagingFinished(bool bSuccess);

    float PackagingProgress = 0.0f;
    FString CurrentPackagingAsset;
    bool bIsPackaging = false;

    TSharedPtr<class FNeoPakPackager> Packager;
};
```

#### 2.2.3 资产类型系统
```cpp
// 基础资产类型接口
class NEOPAKTOOLS_API INeoAssetType
{
public:
    virtual ~INeoAssetType() = default;
    virtual FString GetAssetTypeName() const = 0;
    virtual TArray<FString> GetDependencies() const = 0;
    virtual bool ValidateAsset(const FString& AssetPath) const = 0;
    virtual bool CanPackageIndependently() const = 0;
};

// 资产类型枚举
UENUM(BlueprintType)
enum class ENeoAssetType : uint8
{
    Skeleton    UMETA(DisplayName = "Skeleton Asset"),
    Character   UMETA(DisplayName = "Character Asset"),
    Map         UMETA(DisplayName = "Map Asset"),
    Animation   UMETA(DisplayName = "Animation Asset"),
    Clothing    UMETA(DisplayName = "Clothing Asset")
};
```

#### 2.2.4 PAK管理器 (重构为支持子系统)
```cpp
class NEOPAKTOOLS_API UNeoPakManager : public UObject
{
    GENERATED_BODY()

public:
    // 初始化和清理
    void Initialize(UNeoPakSubsystem* InOwnerSubsystem);
    void Shutdown();

    // 核心PAK操作
    bool LoadPakFile(const FString& PakFilePath);
    bool UnloadPakFile(const FString& PakFilePath);
    bool IsPakLoaded(const FString& PakFilePath) const;

    // PAK信息查询
    TArray<FString> GetLoadedPakFiles() const;
    EPakLoadStatus GetPakLoadStatus(const FString& PakFilePath) const;
    FNeoPakInfo GetPakInfo(const FString& PakFilePath) const;

    // 异步操作
    void LoadPakFileAsync(const FString& PakFilePath, FOnPakOperationComplete OnComplete);
    void UnloadPakFileAsync(const FString& PakFilePath, FOnPakOperationComplete OnComplete);

    DECLARE_DELEGATE_TwoParams(FOnPakOperationComplete, const FString&, bool);

private:
    // 内部PAK操作
    bool MountPakFile(const FString& PakFilePath);
    bool UnmountPakFile(const FString& PakFilePath);
    bool ValidatePakFile(const FString& PakFilePath);

    // 异步任务处理
    void ProcessAsyncTasks();

    UPROPERTY()
    TWeakObjectPtr<UNeoPakSubsystem> OwnerSubsystem;

    TMap<FString, TSharedPtr<class FNeoPakEntry>> LoadedPaks;
    TArray<TSharedPtr<class FAsyncPakTask>> PendingAsyncTasks;

    FCriticalSection PakOperationLock;
};
```

#### 2.2.5 子系统间通信机制
```cpp
// 子系统间消息传递
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoSubsystemMessage
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Message")
    ENeoMessageType MessageType;

    UPROPERTY(BlueprintReadWrite, Category = "Message")
    FString Payload;

    UPROPERTY(BlueprintReadWrite, Category = "Message")
    TMap<FString, FString> Parameters;
};

UENUM(BlueprintType)
enum class ENeoMessageType : uint8
{
    PakLoaded           UMETA(DisplayName = "PAK Loaded"),
    PakUnloaded         UMETA(DisplayName = "PAK Unloaded"),
    AssetLoaded         UMETA(DisplayName = "Asset Loaded"),
    AssetUnloaded       UMETA(DisplayName = "Asset Unloaded"),
    DependencyResolved  UMETA(DisplayName = "Dependency Resolved"),
    PackagingStarted    UMETA(DisplayName = "Packaging Started"),
    PackagingCompleted  UMETA(DisplayName = "Packaging Completed")
};

// 子系统消息总线
class NEOPAKTOOLS_API FNeoSubsystemMessageBus
{
public:
    static FNeoSubsystemMessageBus& Get();

    void Subscribe(ENeoMessageType MessageType, const FOnNeoMessage& Callback);
    void Unsubscribe(ENeoMessageType MessageType, const FOnNeoMessage& Callback);
    void Broadcast(const FNeoSubsystemMessage& Message);

    DECLARE_DELEGATE_OneParam(FOnNeoMessage, const FNeoSubsystemMessage&);

private:
    TMultiMap<ENeoMessageType, FOnNeoMessage> MessageSubscribers;
    FCriticalSection MessageLock;
};
```

#### 2.2.3 依赖解析器
```cpp
class NEOPAKTOOLS_API FNeoAssetDependencyResolver
{
public:
    // 解析资产依赖
    TArray<FString> ResolveDependencies(const FString& AssetPath, ENeoAssetType AssetType);
    
    // 验证依赖完整性
    bool ValidateDependencies(const TArray<FString>& AssetPaths);
    
    // 获取打包顺序
    TArray<FString> GetPackagingOrder(const TArray<FString>& AssetPaths);

private:
    TMap<ENeoAssetType, TArray<ENeoAssetType>> DependencyMap;
};
```

## 3. 子系统设计理念

### 3.1 为什么使用子系统

#### 3.1.1 子系统的优势
- **生命周期管理**: UE子系统自动管理初始化和清理，确保资源正确释放
- **全局访问**: 通过GetSubsystem<>()可以在任何地方访问，无需手动传递引用
- **线程安全**: 子系统在主线程中运行，避免多线程竞争问题
- **模块化**: 不同功能分离到不同子系统，职责清晰，易于维护
- **蓝图友好**: 子系统可以直接在蓝图中访问和使用
- **编辑器集成**: EditorSubsystem可以在编辑器中提供工具功能

#### 3.1.2 子系统类型选择

##### GameInstanceSubsystem
- **用途**: 运行时PAK管理、资产管理、依赖管理
- **生命周期**: 与GameInstance相同，在游戏运行期间持续存在
- **优势**: 跨关卡持久化，适合管理全局资源

##### EditorSubsystem
- **用途**: 编辑器工具、打包功能、资产验证
- **生命周期**: 编辑器启动时创建，关闭时销毁
- **优势**: 编辑器专用功能，不会影响运行时性能

#### 3.1.3 子系统间协作模式
```cpp
// 消息驱动的松耦合设计
class UNeoPakSubsystem : public UGameInstanceSubsystem
{
    virtual void Initialize(FSubsystemCollectionBase& Collection) override
    {
        Super::Initialize(Collection);

        // 获取其他子系统的引用
        AssetManagerSubsystem = GetGameInstance()->GetSubsystem<UNeoAssetManagerSubsystem>();
        DependencySubsystem = GetGameInstance()->GetSubsystem<UNeoDependencySubsystem>();

        // 订阅消息总线
        FNeoSubsystemMessageBus::Get().Subscribe(ENeoMessageType::AssetLoaded,
            FNeoSubsystemMessageBus::FOnNeoMessage::CreateUObject(this, &UNeoPakSubsystem::OnAssetLoaded));
    }

private:
    void OnAssetLoaded(const FNeoSubsystemMessage& Message)
    {
        // 处理资产加载完成消息
        FString AssetPath = Message.Payload;
        UE_LOG(LogNeoPakTools, Log, TEXT("Asset loaded: %s"), *AssetPath);

        // 通知其他系统
        OnPakAssetLoaded.Broadcast(AssetPath);
    }
};
```

### 3.2 子系统配置和设置
```cpp
// 子系统配置类
UCLASS(Config=Game, DefaultConfig)
class NEOPAKTOOLS_API UNeoPakSubsystemSettings : public UDeveloperSettings
{
    GENERATED_BODY()

public:
    UNeoPakSubsystemSettings();

    // 自动加载设置
    UPROPERTY(Config, EditAnywhere, Category = "Auto Loading")
    bool bAutoLoadPaksOnStartup = false;

    UPROPERTY(Config, EditAnywhere, Category = "Auto Loading", meta = (EditCondition = "bAutoLoadPaksOnStartup"))
    TArray<FString> AutoLoadPakPaths;

    // 内存管理设置
    UPROPERTY(Config, EditAnywhere, Category = "Memory Management")
    int32 MaxLoadedAssets = 1000;

    UPROPERTY(Config, EditAnywhere, Category = "Memory Management")
    float AssetUnloadDelay = 30.0f;

    // 依赖管理设置
    UPROPERTY(Config, EditAnywhere, Category = "Dependency Management")
    bool bStrictDependencyChecking = true;

    UPROPERTY(Config, EditAnywhere, Category = "Dependency Management")
    bool bAutoPreloadDependencies = true;

    // 日志设置
    UPROPERTY(Config, EditAnywhere, Category = "Logging")
    bool bVerboseLogging = false;

    UPROPERTY(Config, EditAnywhere, Category = "Logging")
    bool bLogAssetOperations = true;

#if WITH_EDITOR
    // UDeveloperSettings interface
    virtual FText GetSectionText() const override;
    virtual FText GetSectionDescription() const override;
#endif
};
```

## 4. 功能设计

### 4.1 编辑器工具 (基于编辑器子系统)

#### 4.1.1 编辑器子系统集成
```cpp
// 编辑器工具主入口
class NEOPAKTOOLSEDITOR_API FNeoPakToolsEditorModule : public IModuleInterface
{
public:
    virtual void StartupModule() override
    {
        // 注册编辑器菜单
        RegisterMenuExtensions();

        // 注册工具栏
        RegisterToolbarExtensions();

        // 注册资产操作
        RegisterAssetActions();
    }

private:
    void RegisterMenuExtensions();
    void RegisterToolbarExtensions();
    void RegisterAssetActions();

    // 菜单回调
    void OnOpenPakToolsWindow();
    void OnPackageSelectedAssets();
    void OnValidateAssetDependencies();
};

// 编辑器窗口集成
void FNeoPakToolsEditorModule::OnOpenPakToolsWindow()
{
    // 获取编辑器子系统
    UNeoPakEditorSubsystem* EditorSubsystem = GEditor->GetEditorSubsystem<UNeoPakEditorSubsystem>();

    // 创建并显示工具窗口
    TSharedRef<SNeoPakToolsWindow> ToolsWindow = SNew(SNeoPakToolsWindow)
        .EditorSubsystem(EditorSubsystem);

    FGlobalTabmanager::Get()->TryInvokeTab(FName("NeoPakTools"));
}
```

#### 4.1.2 主界面设计 (Slate UI)
```cpp
// 主工具窗口
class NEOPAKTOOLSEDITOR_API SNeoPakToolsWindow : public SCompoundWidget
{
public:
    SLATE_BEGIN_ARGS(SNeoPakToolsWindow) {}
        SLATE_ARGUMENT(UNeoPakEditorSubsystem*, EditorSubsystem)
    SLATE_END_ARGS()

    void Construct(const FArguments& InArgs);

private:
    // UI组件
    TSharedPtr<SAssetBrowser> AssetBrowser;
    TSharedPtr<SPackagingConfigPanel> ConfigPanel;
    TSharedPtr<SDependencyViewer> DependencyViewer;
    TSharedPtr<SProgressPanel> ProgressPanel;
    TSharedPtr<SOutputLog> OutputLog;

    // 子系统引用
    TWeakObjectPtr<UNeoPakEditorSubsystem> EditorSubsystem;

    // UI事件处理
    FReply OnPackageButtonClicked();
    FReply OnValidateButtonClicked();
    FReply OnClearLogButtonClicked();

    // 子系统事件绑定
    void OnPackagingProgress(float Progress, const FString& CurrentAsset, const FString& Status);
    void OnPackagingComplete(bool bSuccess, const FString& OutputPath);
};
```

#### 4.1.3 基于子系统的打包流程
```cpp
// 编辑器中的打包操作
void SNeoPakToolsWindow::OnPackageButtonClicked()
{
    if (!EditorSubsystem.IsValid())
    {
        return FReply::Handled();
    }

    // 获取配置
    FNeoPakConfig Config = ConfigPanel->GetCurrentConfig();

    // 验证配置
    if (!EditorSubsystem->ValidatePackagingConfiguration(Config))
    {
        // 显示错误信息
        FMessageDialog::Open(EAppMsgType::Ok,
            LOCTEXT("InvalidConfig", "Packaging configuration is invalid. Please check your settings."));
        return FReply::Handled();
    }

    // 绑定进度事件
    EditorSubsystem->OnPackagingProgress.AddSP(this, &SNeoPakToolsWindow::OnPackagingProgress);
    EditorSubsystem->OnPackagingComplete.AddSP(this, &SNeoPakToolsWindow::OnPackagingComplete);

    // 开始打包
    bool bStarted = EditorSubsystem->PackageAssets(
        AssetBrowser->GetSelectedAssetPaths(),
        Config.OutputPakPath,
        Config.AssetType
    );

    if (bStarted)
    {
        ProgressPanel->SetVisible(true);
        OutputLog->AddMessage(TEXT("Packaging started..."));
    }
    else
    {
        FMessageDialog::Open(EAppMsgType::Ok,
            LOCTEXT("PackagingFailed", "Failed to start packaging process."));
    }

    return FReply::Handled();
}

// 打包进度更新
void SNeoPakToolsWindow::OnPackagingProgress(float Progress, const FString& CurrentAsset, const FString& Status)
{
    ProgressPanel->SetProgress(Progress);
    ProgressPanel->SetCurrentAsset(CurrentAsset);
    OutputLog->AddMessage(FString::Printf(TEXT("[%.1f%%] %s: %s"), Progress * 100.0f, *CurrentAsset, *Status));
}

// 打包完成处理
void SNeoPakToolsWindow::OnPackagingComplete(bool bSuccess, const FString& OutputPath)
{
    ProgressPanel->SetVisible(false);

    if (bSuccess)
    {
        OutputLog->AddMessage(FString::Printf(TEXT("Packaging completed successfully: %s"), *OutputPath));

        // 显示成功通知
        FNotificationInfo Info(LOCTEXT("PackagingSuccess", "PAK file created successfully!"));
        Info.ExpireDuration = 3.0f;
        FSlateNotificationManager::Get().AddNotification(Info);
    }
    else
    {
        OutputLog->AddMessage(TEXT("Packaging failed!"));

        // 显示错误通知
        FNotificationInfo Info(LOCTEXT("PackagingError", "PAK packaging failed!"));
        Info.ExpireDuration = 5.0f;
        FSlateNotificationManager::Get().AddNotification(Info);
    }

    // 解绑事件
    EditorSubsystem->OnPackagingProgress.RemoveAll(this);
    EditorSubsystem->OnPackagingComplete.RemoveAll(this);
}
```

#### 3.1.3 配置选项
```cpp
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoPakConfig
{
    GENERATED_BODY()

    // 源目录路径
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Packaging")
    FString SourceDirectory;
    
    // 输出PAK文件路径
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Packaging")
    FString OutputPakPath;
    
    // 资产类型
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Packaging")
    ENeoAssetType AssetType;
    
    // 是否包含依赖
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Packaging")
    bool bIncludeDependencies = true;
    
    // 压缩级别
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Packaging")
    int32 CompressionLevel = 6;
    
    // 是否加密
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Packaging")
    bool bEncryptPak = false;
};
```

### 3.2 运行时系统 (基于子系统架构)

#### 3.2.1 子系统使用流程

##### 基本PAK加载流程
```cpp
// 在GameMode或PlayerController中使用
void AMyGameMode::LoadContentPak(const FString& PakPath)
{
    // 获取PAK子系统
    UNeoPakSubsystem* PakSubsystem = GetGameInstance()->GetSubsystem<UNeoPakSubsystem>();
    if (!PakSubsystem)
    {
        UE_LOG(LogGame, Error, TEXT("NeoPakSubsystem not available"));
        return;
    }

    // 绑定加载完成事件
    PakSubsystem->OnPakLoaded.AddDynamic(this, &AMyGameMode::OnPakLoadComplete);

    // 异步加载PAK文件
    bool bStarted = PakSubsystem->LoadPakFile(PakPath);
    if (!bStarted)
    {
        UE_LOG(LogGame, Error, TEXT("Failed to start PAK loading: %s"), *PakPath);
    }
}

UFUNCTION()
void AMyGameMode::OnPakLoadComplete(const FString& PakFilePath, bool bSuccess)
{
    if (bSuccess)
    {
        UE_LOG(LogGame, Log, TEXT("PAK loaded successfully: %s"), *PakFilePath);

        // 获取资产管理子系统
        UNeoAssetManagerSubsystem* AssetManager = GetGameInstance()->GetSubsystem<UNeoAssetManagerSubsystem>();

        // 加载特定资产
        AssetManager->LoadAssetAsync(TEXT("/Game/Characters/NewCharacter"),
            FNeoAssetManagerSubsystem::FOnAssetLoaded::CreateUFunction(this, FName("OnAssetLoaded")));
    }
    else
    {
        UE_LOG(LogGame, Error, TEXT("PAK loading failed: %s"), *PakFilePath);
    }
}
```

##### 依赖管理流程
```cpp
// 检查和预加载依赖
void AMyGameMode::LoadCharacterWithDependencies(const FString& CharacterAssetPath)
{
    UNeoDependencySubsystem* DependencySubsystem = GetGameInstance()->GetSubsystem<UNeoDependencySubsystem>();

    // 检查依赖
    TArray<FString> MissingDeps = DependencySubsystem->GetMissingDependencies(CharacterAssetPath);
    if (MissingDeps.Num() > 0)
    {
        UE_LOG(LogGame, Warning, TEXT("Missing dependencies for %s:"), *CharacterAssetPath);
        for (const FString& Dep : MissingDeps)
        {
            UE_LOG(LogGame, Warning, TEXT("  - %s"), *Dep);
        }

        // 预加载依赖
        DependencySubsystem->PreloadDependencies(CharacterAssetPath);
    }

    // 加载主资产
    UNeoAssetManagerSubsystem* AssetManager = GetGameInstance()->GetSubsystem<UNeoAssetManagerSubsystem>();
    AssetManager->LoadAssetAsync(CharacterAssetPath,
        FNeoAssetManagerSubsystem::FOnAssetLoaded::CreateUFunction(this, FName("OnCharacterLoaded")));
}
```

#### 3.2.2 蓝图集成
```cpp
// 蓝图函数库，提供简化的蓝图接口
UCLASS()
class NEOPAKTOOLS_API UNeoPakBlueprintLibrary : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    // 简化的PAK操作
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools", meta = (CallInEditor = "true"))
    static bool LoadPakFile(const FString& PakFilePath);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    static bool UnloadPakFile(const FString& PakFilePath);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    static TArray<FString> GetLoadedPakFiles();

    // 资产操作
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    static UObject* LoadAssetFromPak(const FString& AssetPath);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    static bool IsAssetLoaded(const FString& AssetPath);

    // 依赖检查
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    static TArray<FString> GetAssetDependencies(const FString& AssetPath);

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    static bool ValidateAssetDependencies(const FString& AssetPath);

private:
    // 内部辅助函数
    static UNeoPakSubsystem* GetPakSubsystem(const UObject* WorldContext);
    static UNeoAssetManagerSubsystem* GetAssetManagerSubsystem(const UObject* WorldContext);
    static UNeoDependencySubsystem* GetDependencySubsystem(const UObject* WorldContext);
};
```

#### 3.2.2 资产注册表
```cpp
class NEOPAKTOOLS_API FNeoAssetRegistry
{
public:
    // 注册已加载的资产
    void RegisterLoadedAsset(const FString& AssetPath, ENeoAssetType AssetType);
    
    // 查找资产
    TArray<FString> FindAssetsByType(ENeoAssetType AssetType);
    
    // 检查资产是否已加载
    bool IsAssetLoaded(const FString& AssetPath);
    
    // 获取资产信息
    FNeoAssetInfo GetAssetInfo(const FString& AssetPath);

private:
    TMap<FString, FNeoAssetInfo> RegisteredAssets;
    TMultiMap<ENeoAssetType, FString> AssetsByType;
};
```

### 3.3 依赖管理

#### 3.3.1 依赖规则
- **骨骼资产**: 无依赖，可独立打包
- **角色资产**: 必须依赖骨骼资产
- **动画资产**: 必须依赖骨骼资产
- **地图资产**: 可能依赖角色、服装等资产
- **服装资产**: 无强制依赖，可独立打包

#### 3.3.2 依赖检查算法
```cpp
bool FNeoAssetDependencyResolver::ValidateDependencies(const TArray<FString>& AssetPaths)
{
    for (const FString& AssetPath : AssetPaths)
    {
        ENeoAssetType AssetType = GetAssetType(AssetPath);
        TArray<FString> Dependencies = GetRequiredDependencies(AssetType);
        
        for (const FString& Dependency : Dependencies)
        {
            if (!IsAssetAvailable(Dependency))
            {
                UE_LOG(LogNeoPakTools, Error, TEXT("Missing dependency: %s for asset: %s"), 
                       *Dependency, *AssetPath);
                return false;
            }
        }
    }
    return true;
}
```

## 4. 用户界面设计

### 4.1 主工具窗口
- **菜单栏**: 文件操作、设置、帮助
- **工具栏**: 常用操作快捷按钮
- **资产列表**: 显示可打包的资产
- **属性面板**: 显示选中资产的详细信息
- **输出日志**: 显示操作日志和错误信息

### 4.2 打包向导
1. **选择资产类型**
2. **选择源目录**
3. **配置打包选项**
4. **依赖检查**
5. **执行打包**
6. **完成确认**

### 4.3 运行时监控面板
- **已加载PAK列表**
- **内存使用情况**
- **加载性能统计**
- **错误和警告信息**

## 5. 性能优化

### 5.1 打包优化
- **并行打包**: 支持多线程并行处理
- **增量打包**: 只打包修改过的资产
- **压缩优化**: 根据资产类型选择最佳压缩算法
- **依赖缓存**: 缓存依赖关系以提高后续打包速度

### 5.2 加载优化
- **异步加载**: 避免阻塞主线程
- **预加载策略**: 智能预测和预加载可能需要的资产
- **内存管理**: 及时释放不需要的资产
- **流式加载**: 支持大型资产的流式加载

## 6. 错误处理和日志

### 6.1 错误类型
- **依赖缺失错误**
- **文件访问错误**
- **内存不足错误**
- **格式不兼容错误**

### 6.2 日志系统
```cpp
DECLARE_LOG_CATEGORY_EXTERN(LogNeoPakTools, Log, All);

// 使用示例
UE_LOG(LogNeoPakTools, Warning, TEXT("Asset dependency missing: %s"), *AssetPath);
UE_LOG(LogNeoPakTools, Error, TEXT("Failed to load PAK file: %s"), *PakFilePath);
```

## 7. 配置和设置

### 7.1 项目设置
- **默认输出路径**
- **压缩设置**
- **加密选项**
- **依赖检查级别**

### 7.2 用户偏好设置
- **界面布局**
- **日志级别**
- **自动保存选项**
- **快捷键配置**

## 8. 扩展性设计

### 8.1 插件接口
```cpp
class NEOPAKTOOLS_API INeoAssetTypePlugin
{
public:
    virtual ~INeoAssetTypePlugin() = default;
    virtual void RegisterAssetType(TSharedPtr<INeoAssetType> AssetType) = 0;
    virtual void UnregisterAssetType(const FString& TypeName) = 0;
};
```

### 8.2 自定义资产类型
支持用户定义新的资产类型和依赖规则，通过插件系统进行扩展。

## 9. 测试策略

### 9.1 单元测试
- 资产类型识别测试
- 依赖解析测试
- PAK文件操作测试

### 9.2 集成测试
- 完整打包流程测试
- 运行时加载测试
- 性能压力测试

### 9.3 用户验收测试
- 界面易用性测试
- 功能完整性测试
- 错误处理测试

## 10. 部署和维护

### 10.1 版本管理
- 语义化版本控制
- 向后兼容性保证
- 升级路径规划

### 10.2 文档和支持
- 用户手册
- API文档
- 示例项目
- 常见问题解答

## 11. 实现计划

### 11.1 开发阶段

#### 阶段1: 核心框架 (4周)
- 基础模块结构搭建
- 资产类型系统实现
- 基础PAK管理器实现

#### 阶段2: 编辑器工具 (6周)
- 编辑器UI界面开发
- 打包流程实现
- 依赖解析器开发

#### 阶段3: 运行时系统 (4周)
- PAK加载器实现
- 资产注册表开发
- 异步加载系统

#### 阶段4: 优化和测试 (4周)
- 性能优化
- 错误处理完善
- 全面测试

#### 阶段5: 文档和发布 (2周)
- 用户文档编写
- 示例项目制作
- 最终发布准备

### 11.2 里程碑
- **M1**: 核心框架完成
- **M2**: 基础打包功能可用
- **M3**: 运行时加载功能完成
- **M4**: Beta版本发布
- **M5**: 正式版本发布

## 12. 风险评估

### 12.1 技术风险
- **UE版本兼容性**: 不同UE版本的PAK格式差异
- **性能瓶颈**: 大量资产加载时的性能问题
- **内存管理**: 运行时内存使用优化

### 12.2 缓解措施
- 多版本测试和兼容性适配
- 性能分析和优化工具使用
- 内存池和智能指针管理

## 13. 详细技术规范

### 13.1 PAK文件格式扩展
```cpp
struct FNeoPakHeader
{
    uint32 Magic;                    // 'NPAK'
    uint32 Version;                  // 版本号
    ENeoAssetType AssetType;         // 资产类型
    uint32 DependencyCount;          // 依赖数量
    uint64 DependencyOffset;         // 依赖信息偏移
    uint64 AssetDataOffset;          // 资产数据偏移
    uint32 Checksum;                 // 校验和
};

struct FNeoDependencyInfo
{
    FString DependencyPath;          // 依赖路径
    ENeoAssetType DependencyType;    // 依赖类型
    uint32 DependencyHash;           // 依赖哈希
};
```

### 13.2 资产加载状态机
```cpp
UENUM(BlueprintType)
enum class EPakLoadStatus : uint8
{
    NotLoaded       UMETA(DisplayName = "Not Loaded"),
    Loading         UMETA(DisplayName = "Loading"),
    Loaded          UMETA(DisplayName = "Loaded"),
    Failed          UMETA(DisplayName = "Failed"),
    Unloading       UMETA(DisplayName = "Unloading")
};
```

### 13.3 蓝图接口设计
```cpp
UCLASS(BlueprintType, Blueprintable)
class NEOPAKTOOLS_API UNeoPakToolsBlueprintLibrary : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    // 蓝图可调用的打包函数
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools", CallInEditor = true)
    static bool PackageAssets(const TArray<FString>& AssetPaths, const FString& OutputPath, ENeoAssetType AssetType);

    // 蓝图可调用的加载函数
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    static bool LoadPakFileBlueprint(const FString& PakFilePath);

    // 获取已加载的PAK列表
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    static TArray<FString> GetLoadedPakFiles();

    // 检查资产是否可用
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    static bool IsAssetAvailable(const FString& AssetPath);
};
```

## 14. 安全和加密

### 14.1 PAK文件加密
- **AES-256加密**: 支持PAK文件内容加密
- **密钥管理**: 安全的密钥存储和分发机制
- **签名验证**: 防止PAK文件被篡改

### 14.2 访问控制
```cpp
class NEOPAKTOOLS_API FNeoSecurityManager
{
public:
    // 验证PAK文件签名
    bool VerifyPakSignature(const FString& PakFilePath);

    // 解密PAK文件
    bool DecryptPakFile(const FString& PakFilePath, const FString& DecryptionKey);

    // 检查访问权限
    bool CheckAccessPermission(const FString& AssetPath, const FString& UserToken);
};
```

## 15. 国际化支持

### 15.1 多语言界面
- 支持中文、英文等多种语言
- 动态语言切换
- 本地化资源管理

### 15.2 本地化配置
```cpp
USTRUCT(BlueprintType)
struct NEOPAKTOOLS_API FNeoLocalizationConfig
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Localization")
    FString DefaultLanguage = TEXT("zh-CN");

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Localization")
    TMap<FString, FString> LanguageDisplayNames;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Localization")
    bool bAutoDetectSystemLanguage = true;
};
```

## 16. 总结

NeoPakTools插件将为Unreal Engine开发者提供一个强大而灵活的资产打包和运行时加载解决方案。通过模块化设计、完善的依赖管理和优化的性能表现，该插件将显著提升开发效率和游戏运行时的资源管理能力。

### 16.1 核心优势
- **类型化资产管理**: 针对不同资产类型的专门处理
- **智能依赖解析**: 自动处理复杂的资产依赖关系
- **高性能加载**: 异步加载和内存优化
- **易用的界面**: 直观的编辑器工具和蓝图接口
- **扩展性强**: 支持自定义资产类型和插件扩展

### 16.2 应用场景
- **DLC内容分发**: 游戏追加内容的打包和分发
- **模块化开发**: 大型项目的模块化资源管理
- **热更新系统**: 运行时资源更新和替换
- **内容流式加载**: 开放世界游戏的动态内容加载

## 17. 子系统最佳实践和使用指南

### 17.1 子系统使用最佳实践

#### 17.1.1 初始化顺序
```cpp
// 在模块启动时确保正确的初始化顺序
void FNeoPakToolsModule::StartupModule()
{
    // 1. 首先注册消息总线
    FNeoSubsystemMessageBus::Get().Initialize();

    // 2. 注册自定义设置
    if (ISettingsModule* SettingsModule = FModuleManager::GetModulePtr<ISettingsModule>("Settings"))
    {
        SettingsModule->RegisterSettings("Project", "Plugins", "NeoPakTools",
            LOCTEXT("RuntimeSettingsName", "Neo Pak Tools"),
            LOCTEXT("RuntimeSettingsDescription", "Configure Neo Pak Tools"),
            GetMutableDefault<UNeoPakSubsystemSettings>()
        );
    }

    // 3. 子系统会在GameInstance创建时自动初始化
}
```

#### 17.1.2 错误处理模式
```cpp
// 统一的错误处理模式
class UNeoPakSubsystem : public UGameInstanceSubsystem
{
private:
    bool HandlePakOperation(const FString& Operation, TFunction<bool()> OperationFunc)
    {
        try
        {
            bool bResult = OperationFunc();
            if (!bResult)
            {
                UE_LOG(LogNeoPakTools, Error, TEXT("%s operation failed"), *Operation);
                BroadcastError(FString::Printf(TEXT("%s failed"), *Operation));
            }
            return bResult;
        }
        catch (const std::exception& e)
        {
            FString ErrorMsg = FString::Printf(TEXT("%s exception: %s"), *Operation, UTF8_TO_TCHAR(e.what()));
            UE_LOG(LogNeoPakTools, Error, TEXT("%s"), *ErrorMsg);
            BroadcastError(ErrorMsg);
            return false;
        }
    }

    void BroadcastError(const FString& ErrorMessage)
    {
        FNeoSubsystemMessage Message;
        Message.MessageType = ENeoMessageType::Error;
        Message.Payload = ErrorMessage;
        FNeoSubsystemMessageBus::Get().Broadcast(Message);
    }
};
```

#### 17.1.3 内存管理
```cpp
// 智能内存管理
class UNeoAssetManagerSubsystem : public UGameInstanceSubsystem
{
private:
    // 使用弱引用避免循环依赖
    TMap<FString, TWeakObjectPtr<UObject>> LoadedAssets;

    // 定期清理无效引用
    FTimerHandle CleanupTimerHandle;

    void PerformCleanup()
    {
        int32 CleanedCount = 0;
        for (auto It = LoadedAssets.CreateIterator(); It; ++It)
        {
            if (!It.Value().IsValid())
            {
                It.RemoveCurrent();
                CleanedCount++;
            }
        }

        if (CleanedCount > 0)
        {
            UE_LOG(LogNeoPakTools, Log, TEXT("Cleaned up %d invalid asset references"), CleanedCount);
        }
    }

public:
    virtual void Initialize(FSubsystemCollectionBase& Collection) override
    {
        Super::Initialize(Collection);

        // 设置定期清理
        GetWorld()->GetTimerManager().SetTimer(CleanupTimerHandle,
            this, &UNeoAssetManagerSubsystem::PerformCleanup,
            60.0f, true); // 每分钟清理一次
    }
};
```

### 17.2 性能优化指南

#### 17.2.1 异步操作模式
```cpp
// 使用异步任务避免阻塞主线程
class FAsyncPakLoadTask : public FNonAbandonableTask
{
public:
    FAsyncPakLoadTask(const FString& InPakPath, TWeakObjectPtr<UNeoPakSubsystem> InSubsystem)
        : PakPath(InPakPath), SubsystemPtr(InSubsystem) {}

    void DoWork()
    {
        // 在后台线程执行PAK加载
        bSuccess = LoadPakFileInternal(PakPath);
    }

    FORCEINLINE TStatId GetStatId() const { RETURN_QUICK_DECLARE_CYCLE_STAT(FAsyncPakLoadTask, STATGROUP_ThreadPoolAsyncTasks); }

private:
    FString PakPath;
    TWeakObjectPtr<UNeoPakSubsystem> SubsystemPtr;
    bool bSuccess = false;

    bool LoadPakFileInternal(const FString& Path);
};

// 在子系统中使用异步任务
void UNeoPakSubsystem::LoadPakFileAsync(const FString& PakFilePath)
{
    auto AsyncTask = new FAsyncTask<FAsyncPakLoadTask>(PakFilePath, this);
    AsyncTask->StartBackgroundTask();

    // 存储任务引用以便后续检查
    PendingAsyncTasks.Add(AsyncTask);
}
```

#### 17.2.2 缓存策略
```cpp
// 智能缓存管理
class UNeoDependencySubsystem : public UGameInstanceSubsystem
{
private:
    // 依赖关系缓存
    TMap<FString, TArray<FString>> DependencyCache;
    TMap<FString, FDateTime> CacheTimestamps;

    static constexpr float CACHE_EXPIRY_SECONDS = 300.0f; // 5分钟过期

public:
    TArray<FString> GetAssetDependencies(const FString& AssetPath) const override
    {
        // 检查缓存
        if (const TArray<FString>* CachedDeps = DependencyCache.Find(AssetPath))
        {
            const FDateTime* Timestamp = CacheTimestamps.Find(AssetPath);
            if (Timestamp && (FDateTime::Now() - *Timestamp).GetTotalSeconds() < CACHE_EXPIRY_SECONDS)
            {
                return *CachedDeps;
            }
        }

        // 重新计算依赖
        TArray<FString> Dependencies = CalculateDependencies(AssetPath);

        // 更新缓存
        DependencyCache.Add(AssetPath, Dependencies);
        CacheTimestamps.Add(AssetPath, FDateTime::Now());

        return Dependencies;
    }
};
```

### 17.3 调试和诊断

#### 17.3.1 子系统状态监控
```cpp
// 调试信息收集
UCLASS()
class NEOPAKTOOLS_API UNeoPakDebugSubsystem : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Debug")
    FString GetSubsystemStatus() const;

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Debug")
    TArray<FString> GetLoadedPakInfo() const;

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Debug")
    FString GetMemoryUsageInfo() const;

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools|Debug")
    void DumpSubsystemState() const;

private:
    void CollectSubsystemMetrics();

    mutable FCriticalSection MetricsLock;
    TMap<FString, FString> CachedMetrics;
};
```

#### 17.3.2 性能分析工具
```cpp
// 性能统计宏
#define NEOPAK_SCOPE_TIMER(TimerName) \
    SCOPE_CYCLE_COUNTER(STAT_NeoPak_##TimerName); \
    FNeoPakScopeTimer ScopeTimer(TEXT(#TimerName))

class FNeoPakScopeTimer
{
public:
    FNeoPakScopeTimer(const FString& InName) : Name(InName), StartTime(FPlatformTime::Seconds()) {}
    ~FNeoPakScopeTimer()
    {
        double ElapsedTime = FPlatformTime::Seconds() - StartTime;
        UE_LOG(LogNeoPakTools, VeryVerbose, TEXT("%s took %.3fms"), *Name, ElapsedTime * 1000.0);
    }

private:
    FString Name;
    double StartTime;
};

// 使用示例
bool UNeoPakSubsystem::LoadPakFile(const FString& PakFilePath)
{
    NEOPAK_SCOPE_TIMER(LoadPakFile);

    // PAK加载逻辑...
    return true;
}
```

---

**文档版本**: 2.0 (更新：加入子系统设计)
**创建日期**: 2025-08-01
**最后更新**: 2025-08-01
**作者**: Neo
**审核状态**: 待审核
**文档类型**: 技术设计文档
