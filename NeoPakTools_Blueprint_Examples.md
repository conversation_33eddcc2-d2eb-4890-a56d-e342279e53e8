# NeoPakTools 蓝图使用示例

## 📋 概述

本文档提供了NeoPakTools插件在蓝图中的详细使用示例，展示如何通过可视化脚本进行PAK文件的打包和加载操作。

## 🎯 蓝图节点说明

### 基础PAK操作节点

#### Load Pak File Simple
- **功能**: 简单的PAK文件加载
- **输入**: PAK文件路径 (String)
- **输出**: 是否成功开始加载 (Boolean)
- **用法**: 在关卡开始时加载必要的PAK文件

#### Is Pak Loaded
- **功能**: 检查PAK文件是否已加载
- **输入**: PAK文件路径 (String)
- **输出**: 是否已加载 (Boolean)
- **用法**: 在加载资产前检查PAK状态

#### Get All Loaded Paks
- **功能**: 获取所有已加载的PAK文件列表
- **输出**: PAK文件路径数组 (Array of String)
- **用法**: 调试和状态监控

#### Unload Pak File
- **功能**: 卸载指定的PAK文件
- **输入**: PAK文件路径 (String)
- **输出**: 是否成功开始卸载 (Boolean)
- **用法**: 内存优化和关卡切换

### 资产管理节点

#### Load Asset By Type
- **功能**: 按类型加载资产
- **输入**: 
  - 资产路径 (String)
  - 资产类型 (ENeoAssetType)
- **输出**: 加载的资产对象 (Object)
- **用法**: 加载特定类型的游戏资产

#### Is Asset Loaded
- **功能**: 检查资产是否已加载
- **输入**: 资产路径 (String)
- **输出**: 是否已加载 (Boolean)
- **用法**: 避免重复加载资产

#### Get Asset Dependencies
- **功能**: 获取资产的依赖列表
- **输入**: 资产路径 (String)
- **输出**: 依赖路径数组 (Array of String)
- **用法**: 依赖关系分析

#### Validate Asset Dependencies
- **功能**: 验证资产依赖完整性
- **输入**: 资产路径 (String)
- **输出**: 依赖是否完整 (Boolean)
- **用法**: 加载前的依赖检查

## 🎮 蓝图使用场景

### 场景1：游戏启动时加载核心PAK

```
Event BeginPlay
    ↓
Load Pak File Simple (D:/GameContent/Core.pak)
    ↓
Branch (成功?)
    ├─ True → Load Pak File Simple (D:/GameContent/Characters.pak)
    └─ False → Print String ("Failed to load Core PAK")
```

### 场景2：关卡切换时的PAK管理

```
Event OnLevelChange
    ↓
Get All Loaded Paks
    ↓
ForEach Loop (遍历已加载的PAK)
    ↓
String Contains (检查是否为关卡PAK)
    ├─ True → Unload Pak File
    └─ False → Continue
    ↓
Load Level Paks (新关卡名称)
```

### 场景3：角色换装系统

```
Event OnCharacterOutfitChange
    ↓
Is Asset Loaded (角色资产路径)
    ├─ True → Load Pak File Simple (服装PAK路径)
    └─ False → 
        ↓
        Load Asset By Type (角色, Character)
        ↓
        Delay (1.0秒)
        ↓
        Load Pak File Simple (服装PAK路径)
```

### 场景4：DLC内容检测和加载

```
Event CheckDLCContent
    ↓
Get All Loaded Paks
    ↓
ForEach Loop
    ↓
String Contains ("DLC")
    ├─ True → Add to Array (DLC列表)
    └─ False → Continue
    ↓
Print String ("Found DLC: " + DLC列表)
```

## 🔧 自定义蓝图事件

### GameMode中的PAK加载事件

在继承自ANeoPakGameModeExample的蓝图中，可以重写以下事件：

#### On Paks Loading Complete
- **触发时机**: 所有初始PAK加载完成
- **用途**: 启动游戏主逻辑
- **示例实现**:
  ```
  Event OnPaksLoadingComplete
      ↓
  Print String ("All PAKs loaded! Starting game...")
      ↓
  Enable Input
      ↓
  Show Main Menu Widget
  ```

### 自定义PAK加载进度显示

```
Event Tick
    ↓
Get Pak Loading Status
    ↓
String Split (分割状态字符串)
    ↓
Convert String to Int (已加载数量)
    ↓
Divide (计算进度百分比)
    ↓
Set Progress Bar Percent
```

## 📊 调试和监控蓝图

### PAK状态监控面板

创建一个Widget蓝图用于显示PAK状态：

```
Widget Blueprint: PAKStatusWidget

Components:
- Text Block: PAK Count
- List View: Loaded PAKs
- Progress Bar: Loading Progress
- Button: Refresh Status

Event Construct:
    ↓
Get All Loaded Paks
    ↓
Set List Items (Loaded PAKs)
    ↓
Set Text (PAK Count)

Event RefreshButtonClicked:
    ↓
Get All Loaded Paks
    ↓
Clear List Items
    ↓
Add Items (更新列表)
```

### 内存使用监控

```
Event Tick (每5秒执行一次)
    ↓
Get Pak Loading Status
    ↓
Get Asset Dependencies Count
    ↓
Format Text ("PAKs: {0}, Assets: {1}")
    ↓
Set Debug Text
```

## ⚡ 性能优化技巧

### 1. 批量PAK操作

```
Event LoadMultiplePAKs
    ↓
Make Array (PAK路径列表)
    ↓
Load Multiple Paks
    ↓
Print String ("Loaded " + 返回值 + " PAKs")
```

### 2. 条件加载

```
Event ConditionalLoad
    ↓
Is Pak Loaded (检查PAK状态)
    ├─ True → Print String ("Already loaded")
    └─ False → Load Pak File Simple
```

### 3. 错误处理

```
Event LoadWithErrorHandling
    ↓
Load Pak File Simple
    ├─ True → Print String ("Loading started")
    └─ False → 
        ↓
        Print String ("Load failed")
        ↓
        Show Error Message Widget
```

## 🎨 UI集成示例

### 主菜单PAK加载界面

```
Widget Blueprint: MainMenuWidget

Event Construct:
    ↓
Set Visibility (Loading Panel, Visible)
    ↓
Set Visibility (Main Menu Panel, Hidden)
    ↓
Bind Event (OnPaksLoadingComplete)

Event OnPaksLoadingComplete:
    ↓
Set Visibility (Loading Panel, Hidden)
    ↓
Set Visibility (Main Menu Panel, Visible)
    ↓
Play Animation (FadeIn)
```

### 设置界面PAK管理

```
Widget Blueprint: SettingsWidget

Event RefreshPAKList:
    ↓
Get All Loaded Paks
    ↓
ForEach Loop
        ↓
    Create Widget (PAKListItem)
        ↓
    Add Child (to ScrollBox)

Event UnloadSelectedPAK:
    ↓
Get Selected Item
    ↓
Unload Pak File
    ↓
Remove Widget
    ↓
Refresh PAK List
```

## 📝 最佳实践

### 1. 错误检查
- 始终检查Load Pak File Simple的返回值
- 使用Is Pak Loaded验证加载状态
- 在加载资产前验证依赖关系

### 2. 性能优化
- 避免在Tick事件中频繁调用PAK操作
- 使用批量操作代替单个操作
- 及时卸载不需要的PAK文件

### 3. 用户体验
- 显示加载进度给用户
- 提供加载失败的错误提示
- 在加载期间禁用相关UI

### 4. 调试支持
- 使用Print String输出关键信息
- 创建调试界面显示PAK状态
- 记录加载时间和性能数据

## 🔍 常见问题解决

### Q: PAK文件加载失败
**A**: 检查文件路径是否正确，确保PAK文件存在且可访问

### Q: 资产加载后无法使用
**A**: 验证资产依赖是否完整，检查资产类型是否匹配

### Q: 内存使用过高
**A**: 定期卸载不需要的PAK文件，使用Optimize Memory Usage节点

### Q: 加载速度慢
**A**: 使用异步加载，避免在主线程中进行大量PAK操作

---

**注意**: 本示例基于NeoPakTools插件的子系统架构设计，确保在使用前已正确安装和配置插件。
