// NeoPakTools 示例代码
// 展示如何使用子系统进行PAK打包和加载

#pragma once

#include "CoreMinimal.h"
#include "Engine/GameInstance.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Subsystems/EditorSubsystem.h"
#include "NeoPakTools_Example_Code.generated.h"

// ==========================================
// 1. 编辑器中打包PAK文件示例
// ==========================================

UCLASS()
class NEOPAKTOOLSEDITOR_API UNeoPakPackagingExample : public UEditorSubsystem
{
    GENERATED_BODY()

public:
    // 打包角色资产示例
    UFUNCTION(CallInEditor = true, Category = "NeoPakTools Examples")
    void PackageCharacterAssets()
    {
        // 获取打包子系统
        UNeoPackagingSubsystem* PackagingSubsystem = GetEditorSubsystem<UNeoPackagingSubsystem>();
        if (!PackagingSubsystem)
        {
            UE_LOG(LogTemp, Error, TEXT("PackagingSubsystem not found!"));
            return;
        }

        // 配置打包参数
        FNeoPakConfig Config;
        Config.SourceDirectory = TEXT("/Game/Characters/");
        Config.OutputPakPath = TEXT("D:/GameContent/Characters.pak");
        Config.AssetType = ENeoAssetType::Character;
        Config.bIncludeDependencies = true;
        Config.CompressionLevel = 6;
        Config.bEncryptPak = false;

        // 绑定打包事件
        UNeoPakEditorSubsystem* EditorSubsystem = GetEditorSubsystem<UNeoPakEditorSubsystem>();
        EditorSubsystem->OnPackagingProgress.AddUObject(this, &UNeoPakPackagingExample::OnPackagingProgress);
        EditorSubsystem->OnPackagingComplete.AddUObject(this, &UNeoPakPackagingExample::OnPackagingComplete);

        // 扫描要打包的资产
        TArray<FString> AssetPaths = EditorSubsystem->ScanAssetsInDirectory(Config.SourceDirectory);
        
        UE_LOG(LogTemp, Log, TEXT("Found %d assets to package"), AssetPaths.Num());
        for (const FString& AssetPath : AssetPaths)
        {
            UE_LOG(LogTemp, Log, TEXT("  - %s"), *AssetPath);
        }

        // 验证配置
        if (!EditorSubsystem->ValidatePackagingConfiguration(Config))
        {
            UE_LOG(LogTemp, Error, TEXT("Invalid packaging configuration!"));
            return;
        }

        // 开始打包
        bool bStarted = EditorSubsystem->PackageAssets(AssetPaths, Config.OutputPakPath, Config.AssetType);
        if (bStarted)
        {
            UE_LOG(LogTemp, Log, TEXT("Packaging started for %s"), *Config.OutputPakPath);
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to start packaging!"));
        }
    }

    // 打包地图资产示例
    UFUNCTION(CallInEditor = true, Category = "NeoPakTools Examples")
    void PackageMapAssets()
    {
        FNeoPakConfig Config;
        Config.SourceDirectory = TEXT("/Game/Maps/");
        Config.OutputPakPath = TEXT("D:/GameContent/Maps.pak");
        Config.AssetType = ENeoAssetType::Map;
        Config.bIncludeDependencies = true;
        Config.CompressionLevel = 8; // 地图使用更高压缩
        Config.bEncryptPak = true;   // 地图加密保护

        UNeoPakEditorSubsystem* EditorSubsystem = GetEditorSubsystem<UNeoPakEditorSubsystem>();
        TArray<FString> AssetPaths = EditorSubsystem->ScanAssetsInDirectory(Config.SourceDirectory);
        
        EditorSubsystem->PackageAssets(AssetPaths, Config.OutputPakPath, Config.AssetType);
    }

    // 批量打包不同类型资产
    UFUNCTION(CallInEditor = true, Category = "NeoPakTools Examples")
    void BatchPackageAssets()
    {
        // 定义打包任务
        TArray<FNeoPakConfig> PackagingTasks = {
            // 骨骼资产
            {TEXT("/Game/Skeletons/"), TEXT("D:/GameContent/Skeletons.pak"), ENeoAssetType::Skeleton, true, 4, false},
            // 角色资产
            {TEXT("/Game/Characters/"), TEXT("D:/GameContent/Characters.pak"), ENeoAssetType::Character, true, 6, false},
            // 动画资产
            {TEXT("/Game/Animations/"), TEXT("D:/GameContent/Animations.pak"), ENeoAssetType::Animation, true, 8, false},
            // 服装资产
            {TEXT("/Game/Clothing/"), TEXT("D:/GameContent/Clothing.pak"), ENeoAssetType::Clothing, false, 6, false},
            // 地图资产
            {TEXT("/Game/Maps/"), TEXT("D:/GameContent/Maps.pak"), ENeoAssetType::Map, true, 8, true}
        };

        UNeoPakEditorSubsystem* EditorSubsystem = GetEditorSubsystem<UNeoPakEditorSubsystem>();
        
        for (const FNeoPakConfig& Config : PackagingTasks)
        {
            TArray<FString> AssetPaths = EditorSubsystem->ScanAssetsInDirectory(Config.SourceDirectory);
            if (AssetPaths.Num() > 0)
            {
                UE_LOG(LogTemp, Log, TEXT("Packaging %s with %d assets"), *Config.OutputPakPath, AssetPaths.Num());
                EditorSubsystem->PackageAssets(AssetPaths, Config.OutputPakPath, Config.AssetType);
            }
            else
            {
                UE_LOG(LogTemp, Warning, TEXT("No assets found in %s"), *Config.SourceDirectory);
            }
        }
    }

private:
    // 打包进度回调
    void OnPackagingProgress(float Progress, const FString& CurrentAsset, const FString& Status)
    {
        UE_LOG(LogTemp, Log, TEXT("Packaging Progress: %.1f%% - %s (%s)"), 
               Progress * 100.0f, *CurrentAsset, *Status);
    }

    // 打包完成回调
    void OnPackagingComplete(bool bSuccess, const FString& OutputPath)
    {
        if (bSuccess)
        {
            UE_LOG(LogTemp, Log, TEXT("Packaging completed successfully: %s"), *OutputPath);
            
            // 验证生成的PAK文件
            ValidateGeneratedPak(OutputPath);
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("Packaging failed for: %s"), *OutputPath);
        }

        // 解绑事件
        UNeoPakEditorSubsystem* EditorSubsystem = GetEditorSubsystem<UNeoPakEditorSubsystem>();
        EditorSubsystem->OnPackagingProgress.RemoveAll(this);
        EditorSubsystem->OnPackagingComplete.RemoveAll(this);
    }

    void ValidateGeneratedPak(const FString& PakPath)
    {
        // 检查文件是否存在
        if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*PakPath))
        {
            int64 FileSize = FPlatformFileManager::Get().GetPlatformFile().FileSize(*PakPath);
            UE_LOG(LogTemp, Log, TEXT("PAK file generated: %s (Size: %lld bytes)"), *PakPath, FileSize);
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("PAK file not found: %s"), *PakPath);
        }
    }
};

// ==========================================
// 2. 运行时加载PAK文件示例
// ==========================================

UCLASS()
class NEOPAKTOOLS_API UNeoPakRuntimeExample : public UObject
{
    GENERATED_BODY()

public:
    // 基础PAK加载示例
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools Examples")
    void LoadCharacterPak(UGameInstance* GameInstance)
    {
        if (!GameInstance)
        {
            UE_LOG(LogTemp, Error, TEXT("GameInstance is null!"));
            return;
        }

        // 获取PAK子系统
        UNeoPakSubsystem* PakSubsystem = GameInstance->GetSubsystem<UNeoPakSubsystem>();
        if (!PakSubsystem)
        {
            UE_LOG(LogTemp, Error, TEXT("NeoPakSubsystem not found!"));
            return;
        }

        // 绑定加载事件
        PakSubsystem->OnPakLoaded.AddDynamic(this, &UNeoPakRuntimeExample::OnPakLoaded);
        PakSubsystem->OnPakUnloaded.AddDynamic(this, &UNeoPakRuntimeExample::OnPakUnloaded);

        // 加载PAK文件
        FString PakPath = TEXT("D:/GameContent/Characters.pak");
        bool bStarted = PakSubsystem->LoadPakFile(PakPath);
        
        if (bStarted)
        {
            UE_LOG(LogTemp, Log, TEXT("Started loading PAK: %s"), *PakPath);
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to start loading PAK: %s"), *PakPath);
        }
    }

    // 带依赖检查的加载示例
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools Examples")
    void LoadCharacterWithDependencies(UGameInstance* GameInstance, const FString& CharacterAssetPath)
    {
        // 获取依赖子系统
        UNeoDependencySubsystem* DependencySubsystem = GameInstance->GetSubsystem<UNeoDependencySubsystem>();
        if (!DependencySubsystem)
        {
            UE_LOG(LogTemp, Error, TEXT("NeoDependencySubsystem not found!"));
            return;
        }

        // 检查依赖
        TArray<FString> Dependencies = DependencySubsystem->GetAssetDependencies(CharacterAssetPath);
        UE_LOG(LogTemp, Log, TEXT("Character %s has %d dependencies:"), *CharacterAssetPath, Dependencies.Num());
        
        for (const FString& Dependency : Dependencies)
        {
            UE_LOG(LogTemp, Log, TEXT("  - %s"), *Dependency);
        }

        // 检查缺失的依赖
        TArray<FString> MissingDeps = DependencySubsystem->GetMissingDependencies(CharacterAssetPath);
        if (MissingDeps.Num() > 0)
        {
            UE_LOG(LogTemp, Warning, TEXT("Missing %d dependencies:"), MissingDeps.Num());
            for (const FString& MissingDep : MissingDeps)
            {
                UE_LOG(LogTemp, Warning, TEXT("  - %s"), *MissingDep);
            }

            // 预加载依赖
            bool bPreloaded = DependencySubsystem->PreloadDependencies(CharacterAssetPath);
            if (bPreloaded)
            {
                UE_LOG(LogTemp, Log, TEXT("Dependencies preloaded successfully"));
            }
        }

        // 验证依赖完整性
        bool bValid = DependencySubsystem->ValidateAssetDependencies(CharacterAssetPath);
        if (bValid)
        {
            // 加载主资产
            LoadAssetFromPak(GameInstance, CharacterAssetPath);
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("Asset dependencies validation failed!"));
        }
    }

    // 异步加载资产示例
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools Examples")
    void LoadAssetFromPak(UGameInstance* GameInstance, const FString& AssetPath)
    {
        UNeoAssetManagerSubsystem* AssetManager = GameInstance->GetSubsystem<UNeoAssetManagerSubsystem>();
        if (!AssetManager)
        {
            UE_LOG(LogTemp, Error, TEXT("NeoAssetManagerSubsystem not found!"));
            return;
        }

        // 检查资产是否已加载
        if (AssetManager->IsAssetLoaded(AssetPath))
        {
            UE_LOG(LogTemp, Log, TEXT("Asset already loaded: %s"), *AssetPath);
            return;
        }

        // 异步加载资产
        UNeoAssetManagerSubsystem::FOnAssetLoaded OnLoaded;
        OnLoaded.BindDynamic(this, &UNeoPakRuntimeExample::OnAssetLoaded);
        
        AssetManager->LoadAssetAsync(AssetPath, OnLoaded);
        UE_LOG(LogTemp, Log, TEXT("Started async loading asset: %s"), *AssetPath);
    }

private:
    // PAK加载完成回调
    UFUNCTION()
    void OnPakLoaded(const FString& PakFilePath, bool bSuccess)
    {
        if (bSuccess)
        {
            UE_LOG(LogTemp, Log, TEXT("PAK loaded successfully: %s"), *PakFilePath);
            
            // 可以在这里触发后续操作，比如加载特定资产
            if (PakFilePath.Contains(TEXT("Characters")))
            {
                // 加载默认角色
                LoadDefaultCharacter();
            }
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("PAK loading failed: %s"), *PakFilePath);
        }
    }

    // PAK卸载完成回调
    UFUNCTION()
    void OnPakUnloaded(const FString& PakFilePath, bool bSuccess)
    {
        if (bSuccess)
        {
            UE_LOG(LogTemp, Log, TEXT("PAK unloaded successfully: %s"), *PakFilePath);
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("PAK unloading failed: %s"), *PakFilePath);
        }
    }

    // 资产加载完成回调
    UFUNCTION()
    void OnAssetLoaded(UObject* LoadedAsset, bool bSuccess)
    {
        if (bSuccess && LoadedAsset)
        {
            UE_LOG(LogTemp, Log, TEXT("Asset loaded successfully: %s"), *LoadedAsset->GetName());
            
            // 处理加载的资产
            if (USkeletalMesh* SkeletalMesh = Cast<USkeletalMesh>(LoadedAsset))
            {
                UE_LOG(LogTemp, Log, TEXT("Loaded skeletal mesh with %d materials"), 
                       SkeletalMesh->GetMaterials().Num());
            }
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("Asset loading failed!"));
        }
    }

    void LoadDefaultCharacter()
    {
        // 实现默认角色加载逻辑
        UE_LOG(LogTemp, Log, TEXT("Loading default character..."));
    }
};

// ==========================================
// 3. 蓝图函数库示例
// ==========================================

UCLASS()
class NEOPAKTOOLS_API UNeoPakBlueprintExamples : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    // 简化的PAK加载接口
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools Examples", meta = (CallInEditor = "true"))
    static bool LoadPakFileSimple(const FString& PakFilePath)
    {
        if (UGameInstance* GameInstance = GetGameInstanceFromWorld())
        {
            UNeoPakSubsystem* PakSubsystem = GameInstance->GetSubsystem<UNeoPakSubsystem>();
            if (PakSubsystem)
            {
                return PakSubsystem->LoadPakFile(PakFilePath);
            }
        }
        return false;
    }

    // 检查PAK是否已加载
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools Examples")
    static bool IsPakLoaded(const FString& PakFilePath)
    {
        if (UGameInstance* GameInstance = GetGameInstanceFromWorld())
        {
            UNeoPakSubsystem* PakSubsystem = GameInstance->GetSubsystem<UNeoPakSubsystem>();
            if (PakSubsystem)
            {
                TArray<FString> LoadedPaks = PakSubsystem->GetLoadedPakFiles();
                return LoadedPaks.Contains(PakFilePath);
            }
        }
        return false;
    }

    // 获取所有已加载的PAK文件
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools Examples")
    static TArray<FString> GetAllLoadedPaks()
    {
        if (UGameInstance* GameInstance = GetGameInstanceFromWorld())
        {
            UNeoPakSubsystem* PakSubsystem = GameInstance->GetSubsystem<UNeoPakSubsystem>();
            if (PakSubsystem)
            {
                return PakSubsystem->GetLoadedPakFiles();
            }
        }
        return TArray<FString>();
    }

    // 加载特定类型的资产
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools Examples")
    static UObject* LoadAssetByType(const FString& AssetPath, ENeoAssetType AssetType)
    {
        if (UGameInstance* GameInstance = GetGameInstanceFromWorld())
        {
            UNeoAssetManagerSubsystem* AssetManager = GameInstance->GetSubsystem<UNeoAssetManagerSubsystem>();
            if (AssetManager)
            {
                return AssetManager->LoadAssetFromPak(AssetPath);
            }
        }
        return nullptr;
    }

    // 批量加载PAK文件
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools Examples")
    static int32 LoadMultiplePaks(const TArray<FString>& PakFilePaths)
    {
        int32 SuccessCount = 0;
        if (UGameInstance* GameInstance = GetGameInstanceFromWorld())
        {
            UNeoPakSubsystem* PakSubsystem = GameInstance->GetSubsystem<UNeoPakSubsystem>();
            if (PakSubsystem)
            {
                for (const FString& PakPath : PakFilePaths)
                {
                    if (PakSubsystem->LoadPakFile(PakPath))
                    {
                        SuccessCount++;
                    }
                }
            }
        }
        return SuccessCount;
    }

    // 卸载PAK文件
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools Examples")
    static bool UnloadPakFile(const FString& PakFilePath)
    {
        if (UGameInstance* GameInstance = GetGameInstanceFromWorld())
        {
            UNeoPakSubsystem* PakSubsystem = GameInstance->GetSubsystem<UNeoPakSubsystem>();
            if (PakSubsystem)
            {
                return PakSubsystem->UnloadPakFile(PakFilePath);
            }
        }
        return false;
    }

    // 获取PAK加载状态
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools Examples")
    static EPakLoadStatus GetPakLoadStatus(const FString& PakFilePath)
    {
        if (UGameInstance* GameInstance = GetGameInstanceFromWorld())
        {
            UNeoPakSubsystem* PakSubsystem = GameInstance->GetSubsystem<UNeoPakSubsystem>();
            if (PakSubsystem)
            {
                return PakSubsystem->GetPakLoadStatus(PakFilePath);
            }
        }
        return EPakLoadStatus::NotLoaded;
    }

    // 验证资产依赖
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools Examples")
    static bool ValidateAssetDependencies(const FString& AssetPath)
    {
        if (UGameInstance* GameInstance = GetGameInstanceFromWorld())
        {
            UNeoDependencySubsystem* DependencySubsystem = GameInstance->GetSubsystem<UNeoDependencySubsystem>();
            if (DependencySubsystem)
            {
                return DependencySubsystem->ValidateAssetDependencies(AssetPath);
            }
        }
        return false;
    }

    // 获取资产依赖列表
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools Examples")
    static TArray<FString> GetAssetDependencies(const FString& AssetPath)
    {
        if (UGameInstance* GameInstance = GetGameInstanceFromWorld())
        {
            UNeoDependencySubsystem* DependencySubsystem = GameInstance->GetSubsystem<UNeoDependencySubsystem>();
            if (DependencySubsystem)
            {
                return DependencySubsystem->GetAssetDependencies(AssetPath);
            }
        }
        return TArray<FString>();
    }

    // 检查资产是否已加载
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools Examples")
    static bool IsAssetLoaded(const FString& AssetPath)
    {
        if (UGameInstance* GameInstance = GetGameInstanceFromWorld())
        {
            UNeoAssetManagerSubsystem* AssetManager = GameInstance->GetSubsystem<UNeoAssetManagerSubsystem>();
            if (AssetManager)
            {
                return AssetManager->IsAssetLoaded(AssetPath);
            }
        }
        return false;
    }

private:
    static UGameInstance* GetGameInstanceFromWorld()
    {
        if (UWorld* World = GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull))
        {
            return World->GetGameInstance();
        }
        return nullptr;
    }
};

// ==========================================
// 4. 游戏模式集成示例
// ==========================================

UCLASS()
class NEOPAKTOOLS_API ANeoPakGameModeExample : public AGameModeBase
{
    GENERATED_BODY()

public:
    ANeoPakGameModeExample()
    {
        // 设置默认的PAK配置
        AutoLoadPaks = {
            TEXT("D:/GameContent/Core.pak"),
            TEXT("D:/GameContent/Characters.pak"),
            TEXT("D:/GameContent/Skeletons.pak")
        };

        LoadedPakCount = 0;
    }

protected:
    virtual void BeginPlay() override
    {
        Super::BeginPlay();

        // 游戏开始时自动加载PAK文件
        LoadInitialPaks();
    }

    // 自动加载的PAK列表
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "NeoPakTools")
    TArray<FString> AutoLoadPaks;

    // 当前关卡需要的PAK文件
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "NeoPakTools")
    TArray<FString> LevelSpecificPaks;

    // 已加载的PAK计数
    UPROPERTY(BlueprintReadOnly, Category = "NeoPakTools")
    int32 LoadedPakCount;

private:
    void LoadInitialPaks()
    {
        UNeoPakSubsystem* PakSubsystem = GetGameInstance()->GetSubsystem<UNeoPakSubsystem>();
        if (!PakSubsystem)
        {
            UE_LOG(LogTemp, Error, TEXT("NeoPakSubsystem not available in GameMode!"));
            return;
        }

        // 绑定PAK加载事件
        PakSubsystem->OnPakLoaded.AddDynamic(this, &ANeoPakGameModeExample::OnInitialPakLoaded);

        // 加载核心PAK文件
        for (const FString& PakPath : AutoLoadPaks)
        {
            UE_LOG(LogTemp, Log, TEXT("Loading initial PAK: %s"), *PakPath);
            PakSubsystem->LoadPakFile(PakPath);
        }
    }

    UFUNCTION()
    void OnInitialPakLoaded(const FString& PakFilePath, bool bSuccess)
    {
        if (bSuccess)
        {
            LoadedPakCount++;
            UE_LOG(LogTemp, Log, TEXT("Initial PAK loaded (%d/%d): %s"),
                   LoadedPakCount, AutoLoadPaks.Num(), *PakFilePath);

            // 检查是否所有初始PAK都已加载
            if (LoadedPakCount >= AutoLoadPaks.Num())
            {
                OnAllInitialPaksLoaded();
            }
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to load initial PAK: %s"), *PakFilePath);
        }
    }

    // 所有初始PAK加载完成后的回调
    virtual void OnAllInitialPaksLoaded()
    {
        UE_LOG(LogTemp, Log, TEXT("All initial PAKs loaded successfully! Game ready to start."));

        // 加载默认角色
        LoadDefaultPlayerCharacter();

        // 通知蓝图
        OnPaksLoadingComplete();
    }

    void LoadDefaultPlayerCharacter()
    {
        FString DefaultCharacterPath = TEXT("/Game/Characters/DefaultPlayer");

        UNeoAssetManagerSubsystem* AssetManager = GetGameInstance()->GetSubsystem<UNeoAssetManagerSubsystem>();
        if (AssetManager)
        {
            UNeoAssetManagerSubsystem::FOnAssetLoaded OnLoaded;
            OnLoaded.BindDynamic(this, &ANeoPakGameModeExample::OnDefaultCharacterLoaded);

            AssetManager->LoadAssetAsync(DefaultCharacterPath, OnLoaded);
        }
    }

    UFUNCTION()
    void OnDefaultCharacterLoaded(UObject* LoadedAsset, bool bSuccess)
    {
        if (bSuccess && LoadedAsset)
        {
            UE_LOG(LogTemp, Log, TEXT("Default character loaded: %s"), *LoadedAsset->GetName());
            // 设置为默认Pawn类
            if (UClass* CharacterClass = Cast<UClass>(LoadedAsset))
            {
                DefaultPawnClass = CharacterClass;
            }
        }
    }

public:
    // 蓝图事件：PAK加载完成
    UFUNCTION(BlueprintImplementableEvent, Category = "NeoPakTools")
    void OnPaksLoadingComplete();

    // 蓝图可调用的PAK管理函数
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    void LoadLevelPaks(const FString& LevelName)
    {
        // 根据关卡名称加载特定的PAK文件
        FString LevelPakPath = FString::Printf(TEXT("D:/GameContent/Levels/%s.pak"), *LevelName);

        UNeoPakSubsystem* PakSubsystem = GetGameInstance()->GetSubsystem<UNeoPakSubsystem>();
        if (PakSubsystem)
        {
            bool bStarted = PakSubsystem->LoadPakFile(LevelPakPath);
            UE_LOG(LogTemp, Log, TEXT("Loading level PAK %s: %s"),
                   *LevelPakPath, bStarted ? TEXT("Started") : TEXT("Failed"));
        }
    }

    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    void UnloadLevelPaks(const FString& LevelName)
    {
        FString LevelPakPath = FString::Printf(TEXT("D:/GameContent/Levels/%s.pak"), *LevelName);

        UNeoPakSubsystem* PakSubsystem = GetGameInstance()->GetSubsystem<UNeoPakSubsystem>();
        if (PakSubsystem)
        {
            bool bStarted = PakSubsystem->UnloadPakFile(LevelPakPath);
            UE_LOG(LogTemp, Log, TEXT("Unloading level PAK %s: %s"),
                   *LevelPakPath, bStarted ? TEXT("Started") : TEXT("Failed"));
        }
    }

    // 获取PAK加载状态信息
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    FString GetPakLoadingStatus()
    {
        UNeoPakSubsystem* PakSubsystem = GetGameInstance()->GetSubsystem<UNeoPakSubsystem>();
        if (PakSubsystem)
        {
            TArray<FString> LoadedPaks = PakSubsystem->GetLoadedPakFiles();
            return FString::Printf(TEXT("Loaded PAKs: %d/%d"), LoadedPaks.Num(), AutoLoadPaks.Num());
        }
        return TEXT("PAK Subsystem not available");
    }

    // 重新加载所有PAK文件
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools")
    void ReloadAllPaks()
    {
        UNeoPakSubsystem* PakSubsystem = GetGameInstance()->GetSubsystem<UNeoPakSubsystem>();
        if (PakSubsystem)
        {
            // 先卸载所有PAK
            TArray<FString> LoadedPaks = PakSubsystem->GetLoadedPakFiles();
            for (const FString& PakPath : LoadedPaks)
            {
                PakSubsystem->UnloadPakFile(PakPath);
            }

            // 重置计数器
            LoadedPakCount = 0;

            // 重新加载
            LoadInitialPaks();
        }
    }
};

// ==========================================
// 5. 实际使用场景示例
// ==========================================

UCLASS()
class NEOPAKTOOLS_API UNeoPakUseCaseExamples : public UObject
{
    GENERATED_BODY()

public:
    // 场景1：DLC内容加载
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools Use Cases")
    void LoadDLCContent(UGameInstance* GameInstance, const FString& DLCName)
    {
        UE_LOG(LogTemp, Log, TEXT("Loading DLC: %s"), *DLCName);

        // DLC PAK文件路径
        FString DLCPakPath = FString::Printf(TEXT("D:/GameContent/DLC/%s.pak"), *DLCName);

        UNeoPakSubsystem* PakSubsystem = GameInstance->GetSubsystem<UNeoPakSubsystem>();
        UNeoDependencySubsystem* DependencySubsystem = GameInstance->GetSubsystem<UNeoDependencySubsystem>();

        if (PakSubsystem && DependencySubsystem)
        {
            // 检查DLC依赖
            TArray<FString> DLCAssets = {
                FString::Printf(TEXT("/Game/DLC/%s/Characters/"), *DLCName),
                FString::Printf(TEXT("/Game/DLC/%s/Maps/"), *DLCName)
            };

            // 验证依赖
            bool bDependenciesValid = true;
            for (const FString& AssetPath : DLCAssets)
            {
                if (!DependencySubsystem->ValidateAssetDependencies(AssetPath))
                {
                    bDependenciesValid = false;
                    break;
                }
            }

            if (bDependenciesValid)
            {
                // 加载DLC PAK
                PakSubsystem->LoadPakFile(DLCPakPath);
                UE_LOG(LogTemp, Log, TEXT("DLC PAK loading started: %s"), *DLCPakPath);
            }
            else
            {
                UE_LOG(LogTemp, Error, TEXT("DLC dependencies validation failed for: %s"), *DLCName);
            }
        }
    }

    // 场景2：关卡流式加载
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools Use Cases")
    void StreamLoadLevel(UGameInstance* GameInstance, const FString& LevelName)
    {
        UE_LOG(LogTemp, Log, TEXT("Stream loading level: %s"), *LevelName);

        // 关卡相关的PAK文件
        TArray<FString> LevelPaks = {
            FString::Printf(TEXT("D:/GameContent/Levels/%s_Geometry.pak"), *LevelName),
            FString::Printf(TEXT("D:/GameContent/Levels/%s_Lighting.pak"), *LevelName),
            FString::Printf(TEXT("D:/GameContent/Levels/%s_Audio.pak"), *LevelName)
        };

        UNeoPakSubsystem* PakSubsystem = GameInstance->GetSubsystem<UNeoPakSubsystem>();
        if (PakSubsystem)
        {
            // 按优先级加载
            for (int32 i = 0; i < LevelPaks.Num(); i++)
            {
                const FString& PakPath = LevelPaks[i];
                bool bStarted = PakSubsystem->LoadPakFile(PakPath);

                UE_LOG(LogTemp, Log, TEXT("Loading level PAK %d/%d: %s (%s)"),
                       i + 1, LevelPaks.Num(), *PakPath, bStarted ? TEXT("Started") : TEXT("Failed"));
            }
        }
    }

    // 场景3：角色换装系统
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools Use Cases")
    void LoadCharacterOutfit(UGameInstance* GameInstance, const FString& CharacterName, const FString& OutfitName)
    {
        UE_LOG(LogTemp, Log, TEXT("Loading outfit %s for character %s"), *OutfitName, *CharacterName);

        // 服装PAK路径
        FString OutfitPakPath = FString::Printf(TEXT("D:/GameContent/Outfits/%s_%s.pak"), *CharacterName, *OutfitName);

        UNeoPakSubsystem* PakSubsystem = GameInstance->GetSubsystem<UNeoPakSubsystem>();
        UNeoAssetManagerSubsystem* AssetManager = GameInstance->GetSubsystem<UNeoAssetManagerSubsystem>();

        if (PakSubsystem && AssetManager)
        {
            // 先检查角色是否已加载
            FString CharacterAssetPath = FString::Printf(TEXT("/Game/Characters/%s"), *CharacterName);
            if (!AssetManager->IsAssetLoaded(CharacterAssetPath))
            {
                UE_LOG(LogTemp, Warning, TEXT("Character %s not loaded, loading first..."), *CharacterName);
                AssetManager->LoadAssetFromPak(CharacterAssetPath);
            }

            // 加载服装PAK
            bool bStarted = PakSubsystem->LoadPakFile(OutfitPakPath);
            if (bStarted)
            {
                // 加载服装资产
                FString OutfitAssetPath = FString::Printf(TEXT("/Game/Outfits/%s_%s"), *CharacterName, *OutfitName);
                AssetManager->LoadAssetFromPak(OutfitAssetPath);
            }
        }
    }

    // 场景4：热更新内容
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools Use Cases")
    void ApplyHotfixContent(UGameInstance* GameInstance, const FString& HotfixVersion)
    {
        UE_LOG(LogTemp, Log, TEXT("Applying hotfix: %s"), *HotfixVersion);

        FString HotfixPakPath = FString::Printf(TEXT("D:/GameContent/Hotfix/%s.pak"), *HotfixVersion);

        UNeoPakSubsystem* PakSubsystem = GameInstance->GetSubsystem<UNeoPakSubsystem>();
        if (PakSubsystem)
        {
            // 检查热更新文件是否存在
            if (FPlatformFileManager::Get().GetPlatformFile().FileExists(*HotfixPakPath))
            {
                // 卸载旧的热更新（如果有）
                TArray<FString> LoadedPaks = PakSubsystem->GetLoadedPakFiles();
                for (const FString& LoadedPak : LoadedPaks)
                {
                    if (LoadedPak.Contains(TEXT("Hotfix/")))
                    {
                        UE_LOG(LogTemp, Log, TEXT("Unloading old hotfix: %s"), *LoadedPak);
                        PakSubsystem->UnloadPakFile(LoadedPak);
                    }
                }

                // 加载新的热更新
                bool bStarted = PakSubsystem->LoadPakFile(HotfixPakPath);
                UE_LOG(LogTemp, Log, TEXT("Hotfix loading %s: %s"),
                       *HotfixVersion, bStarted ? TEXT("Started") : TEXT("Failed"));
            }
            else
            {
                UE_LOG(LogTemp, Error, TEXT("Hotfix file not found: %s"), *HotfixPakPath);
            }
        }
    }

    // 场景5：内存优化 - 按需加载卸载
    UFUNCTION(BlueprintCallable, Category = "NeoPakTools Use Cases")
    void OptimizeMemoryUsage(UGameInstance* GameInstance)
    {
        UE_LOG(LogTemp, Log, TEXT("Optimizing memory usage..."));

        UNeoPakSubsystem* PakSubsystem = GameInstance->GetSubsystem<UNeoPakSubsystem>();
        UNeoAssetManagerSubsystem* AssetManager = GameInstance->GetSubsystem<UNeoAssetManagerSubsystem>();

        if (PakSubsystem && AssetManager)
        {
            // 获取当前加载的PAK文件
            TArray<FString> LoadedPaks = PakSubsystem->GetLoadedPakFiles();

            // 检查每个PAK的使用情况
            for (const FString& PakPath : LoadedPaks)
            {
                // 如果是非核心PAK且长时间未使用，考虑卸载
                if (!PakPath.Contains(TEXT("Core")) && !PakPath.Contains(TEXT("Essential")))
                {
                    // 这里可以添加使用时间检查逻辑
                    bool bShouldUnload = CheckIfPakShouldBeUnloaded(PakPath);
                    if (bShouldUnload)
                    {
                        UE_LOG(LogTemp, Log, TEXT("Unloading unused PAK: %s"), *PakPath);
                        PakSubsystem->UnloadPakFile(PakPath);
                    }
                }
            }

            // 清理未使用的资产
            AssetManager->UnloadAsset(TEXT(""));  // 触发清理
        }
    }

private:
    bool CheckIfPakShouldBeUnloaded(const FString& PakPath)
    {
        // 实现PAK使用情况检查逻辑
        // 这里可以检查最后访问时间、内存使用情况等
        return false;  // 简化示例，实际应该有具体的判断逻辑
    }
};

// ==========================================
// 6. 使用说明和最佳实践
// ==========================================

/*
使用说明：

1. 编辑器中打包PAK：
   - 使用 UNeoPakPackagingExample::PackageCharacterAssets() 打包角色资产
   - 使用 UNeoPakPackagingExample::BatchPackageAssets() 批量打包不同类型资产

2. 运行时加载PAK：
   - 在GameMode的BeginPlay中调用 LoadInitialPaks() 加载核心PAK
   - 使用 UNeoPakRuntimeExample::LoadCharacterWithDependencies() 加载带依赖检查的资产

3. 蓝图集成：
   - 使用 UNeoPakBlueprintExamples 中的函数进行简化的PAK操作
   - 在蓝图中监听 OnPaksLoadingComplete 事件

4. 实际应用场景：
   - DLC加载：LoadDLCContent()
   - 关卡流式加载：StreamLoadLevel()
   - 角色换装：LoadCharacterOutfit()
   - 热更新：ApplyHotfixContent()
   - 内存优化：OptimizeMemoryUsage()

最佳实践：
- 始终检查子系统是否可用
- 使用事件回调处理异步操作
- 验证依赖关系后再加载资产
- 定期清理未使用的PAK和资产
- 使用适当的压缩级别和加密设置
- 在编辑器中测试打包配置的有效性
*/
